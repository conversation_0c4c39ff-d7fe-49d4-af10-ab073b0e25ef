const mysql = require('mysql2/promise');

async function testPointsData() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'loomrun',
      charset: 'utf8mb4'
    });

    console.log('🔗 数据库连接成功');

    // 1. 检查所有用户
    const [users] = await connection.execute(
      'SELECT id, nickname, points, total_earned_points, total_spent_points, created_at FROM users ORDER BY id DESC LIMIT 5'
    );
    console.log('\n📊 最近的用户信息:');
    console.table(users);

    // 2. 检查积分交易记录
    const [transactions] = await connection.execute(
      'SELECT id, user_id, transaction_type, points_amount, source_type, points_type, description, created_at FROM points_transactions ORDER BY created_at DESC LIMIT 10'
    );
    console.log('\n💰 积分交易记录:');
    console.table(transactions);

    // 3. 检查积分余额记录
    const [balances] = await connection.execute(
      'SELECT id, user_id, points_type, points_amount, expires_at, is_active, created_at FROM user_points_balance ORDER BY created_at DESC LIMIT 10'
    );
    console.log('\n💳 积分余额记录:');
    console.table(balances);

    // 4. 检查系统设置
    const [settings] = await connection.execute(
      "SELECT setting_key, setting_value, is_active FROM system_settings WHERE setting_key LIKE '%points%' OR setting_key LIKE '%user%'"
    );
    console.log('\n⚙️ 系统设置:');
    console.table(settings);

    // 5. 如果没有积分记录，为第一个用户创建测试数据
    if (transactions.length === 0 && users.length > 0) {
      const testUserId = users[0].id;
      console.log(`\n🎯 为用户 ${testUserId} 创建测试积分记录...`);

      await connection.beginTransaction();

      try {
        // 创建积分余额记录
        const [balanceResult] = await connection.execute(
          `INSERT INTO user_points_balance 
           (user_id, points_type, points_amount, expires_at) 
           VALUES (?, 'activity', 100, DATE_ADD(NOW(), INTERVAL 30 DAY))`,
          [testUserId]
        );

        const balanceRecordId = balanceResult.insertId;

        // 创建积分交易记录
        await connection.execute(
          `INSERT INTO points_transactions 
           (user_id, transaction_type, points_amount, balance_before, balance_after, source_type, points_type, expires_at, balance_record_id, description, metadata) 
           VALUES (?, 'earn', 100, 0, 100, 'registration', 'activity', DATE_ADD(NOW(), INTERVAL 30 DAY), ?, '新用户注册奖励 100 积分', ?)`,
          [testUserId, balanceRecordId, JSON.stringify({
            registration_bonus: true,
            validity_days: 30,
            registration_type: 'phone',
            grant_timestamp: new Date().toISOString()
          })]
        );

        // 更新用户积分
        await connection.execute(
          'UPDATE users SET points = 100, total_earned_points = 100 WHERE id = ?',
          [testUserId]
        );

        await connection.commit();
        console.log(`✅ 测试积分记录创建成功`);

        // 再次查询验证
        const [newTransactions] = await connection.execute(
          'SELECT id, user_id, transaction_type, points_amount, source_type, points_type, description, created_at FROM points_transactions WHERE user_id = ? ORDER BY created_at DESC',
          [testUserId]
        );
        console.log('\n🎉 新创建的积分记录:');
        console.table(newTransactions);

      } catch (error) {
        await connection.rollback();
        console.error('❌ 创建测试数据失败:', error);
      }
    }

  } catch (error) {
    console.error('❌ 数据库操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
}

// 运行测试
testPointsData().catch(console.error);
