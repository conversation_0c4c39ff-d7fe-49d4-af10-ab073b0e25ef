"use client"

import * as React from "react"
import { Monitor, Moon, Sun } from "lucide-react"
import { cn } from "@/lib/utils"
import { useThemeLanguage } from "@/components/providers/theme-language-provider"

export type ThemeMode = 'system' | 'light' | 'dark'

interface ThemeToggleThreeStateProps {
  className?: string;
}

export function ThemeToggleThreeState({ className }: ThemeToggleThreeStateProps) {
  const { theme, changeTheme } = useThemeLanguage()
  
  // 从localStorage获取当前的主题模式，如果没有则默认为system
  const [currentMode, setCurrentMode] = React.useState<ThemeMode>('system')
  
  React.useEffect(() => {
    const savedMode = localStorage.getItem('theme-mode') as ThemeMode
    if (savedMode && ['system', 'light', 'dark'].includes(savedMode)) {
      setCurrentMode(savedMode)
    } else {
      // 根据当前主题推断模式
      setCurrentMode(theme === 'light' ? 'light' : 'dark')
    }
  }, [theme])

  // 监听系统主题变化
  React.useEffect(() => {
    if (currentMode !== 'system') return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = (e: MediaQueryListEvent) => {
      const systemTheme = e.matches ? 'dark' : 'light'
      changeTheme(systemTheme)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [currentMode, changeTheme])

  const handleModeChange = (mode: ThemeMode) => {
    setCurrentMode(mode)
    localStorage.setItem('theme-mode', mode)
    
    if (mode === 'system') {
      // 跟随系统
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      changeTheme(systemTheme)
    } else {
      // 直接设置主题
      changeTheme(mode)
    }
  }

  const modes: { value: ThemeMode; icon: React.ComponentType<{ className?: string }>; label: string }[] = [
    { value: 'system', icon: Monitor, label: '跟随' },
    { value: 'light', icon: Sun, label: '浅色' },
    { value: 'dark', icon: Moon, label: '深色' },
  ]

  return (
    <div className={cn("flex items-center bg-muted rounded-lg p-1", className)}>
      {modes.map(({ value, icon: Icon, label }) => (
        <button
          key={value}
          onClick={() => handleModeChange(value)}
          className={cn(
            "flex items-center justify-center w-8 h-6 rounded-md transition-all duration-200",
            currentMode === value
              ? "bg-background shadow-sm text-foreground"
              : "text-muted-foreground hover:text-foreground"
          )}
          title={label}
        >
          <Icon className="w-3.5 h-3.5" />
        </button>
      ))}
    </div>
  )
}
