"use client";
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ChevronUp, ChevronDown, Target } from 'lucide-react';

interface MobileScrollControlProps {
  iframeRef: React.RefObject<HTMLIFrameElement | null>;
  isEditMode: boolean;
  onElementSelect: (element: HTMLElement) => void;
}

export const MobileScrollControl: React.FC<MobileScrollControlProps> = ({
  iframeRef,
  isEditMode,
  onElementSelect
}) => {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScroll, setMaxScroll] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [currentElementInfo, setCurrentElementInfo] = useState<{
    tagName: string;
    text: string;
  } | null>(null);
  
  const trackRef = useRef<HTMLDivElement>(null);
  const thumbRef = useRef<HTMLDivElement>(null);

  // 计算滚动条的最大高度和位置
  const updateScrollMetrics = useCallback(() => {
    if (!iframeRef.current?.contentDocument) return;
    
    const doc = iframeRef.current.contentDocument;
    const body = doc.body;
    if (!body) return;
    
    const scrollHeight = body.scrollHeight;
    const clientHeight = doc.documentElement.clientHeight;
    const newMaxScroll = Math.max(0, scrollHeight - clientHeight);
    
    setMaxScroll(newMaxScroll);
  }, [iframeRef]);

  // 获取当前滚动位置
  const getCurrentScroll = useCallback(() => {
    if (!iframeRef.current?.contentDocument) return 0;
    return iframeRef.current.contentDocument.documentElement.scrollTop || 0;
  }, [iframeRef]);

  // 设置滚动位置
  const setIframeScroll = useCallback((position: number) => {
    if (!iframeRef.current?.contentDocument) return;
    
    const clampedPosition = Math.max(0, Math.min(maxScroll, position));
    iframeRef.current.contentDocument.documentElement.scrollTop = clampedPosition;
    setScrollPosition(clampedPosition);
  }, [iframeRef, maxScroll]);

  // 监听iframe滚动事件
  useEffect(() => {
    if (!iframeRef.current?.contentDocument || !isEditMode) return;
    
    const doc = iframeRef.current.contentDocument;
    
    const handleScroll = () => {
      const currentScroll = getCurrentScroll();
      setScrollPosition(currentScroll);
    };
    
    const handleLoad = () => {
      updateScrollMetrics();
      
      // 添加点击事件监听
      doc.addEventListener('click', handleElementClick, true);
      
      // 添加鼠标悬停事件
      doc.addEventListener('mouseover', handleElementHover, true);
      doc.addEventListener('mouseout', handleElementOut, true);
    };
    
    const handleElementClick = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();
      
      const target = e.target as HTMLElement;
      if (target && target.tagName !== 'BODY' && target.tagName !== 'HTML') {
        // 移除所有高亮
        doc.querySelectorAll('.mobile-edit-selected').forEach(el => {
          el.classList.remove('mobile-edit-selected');
        });
        
        // 高亮选中元素
        target.classList.add('mobile-edit-selected');
        
        // 通知父组件
        onElementSelect(target);
      }
    };
    
    const handleElementHover = (e: Event) => {
      const target = e.target as HTMLElement;
      if (target && target.tagName !== 'BODY' && target.tagName !== 'HTML') {
        // 移除之前的悬停高亮
        doc.querySelectorAll('.mobile-edit-hover').forEach(el => {
          el.classList.remove('mobile-edit-hover');
        });
        
        // 添加悬停高亮
        target.classList.add('mobile-edit-hover');
        
        // 更新元素信息
        const text = target.textContent?.trim().substring(0, 30) || '';
        setCurrentElementInfo({
          tagName: target.tagName.toLowerCase(),
          text: text
        });
      }
    };
    
    const handleElementOut = () => {
      // 移除悬停高亮
      doc.querySelectorAll('.mobile-edit-hover').forEach(el => {
        el.classList.remove('mobile-edit-hover');
      });
      setCurrentElementInfo(null);
    };
    
    doc.addEventListener('scroll', handleScroll);
    
    // 初始化
    if (doc.readyState === 'complete') {
      handleLoad();
    } else {
      doc.addEventListener('DOMContentLoaded', handleLoad);
    }
    
    return () => {
      doc.removeEventListener('scroll', handleScroll);
      doc.removeEventListener('DOMContentLoaded', handleLoad);
      doc.removeEventListener('click', handleElementClick, true);
      doc.removeEventListener('mouseover', handleElementHover, true);
      doc.removeEventListener('mouseout', handleElementOut, true);
    };
  }, [iframeRef, isEditMode, updateScrollMetrics, getCurrentScroll, onElementSelect]);

  // 拖拽处理
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!trackRef.current) return;
      
      const rect = trackRef.current.getBoundingClientRect();
      const y = e.clientY - rect.top;
      const percentage = Math.max(0, Math.min(1, y / rect.height));
      const newPosition = percentage * maxScroll;
      
      setIframeScroll(newPosition);
    };
    
    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [maxScroll, setIframeScroll]);

  // 触摸处理
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    setIsDragging(true);
    
    const handleTouchMove = (e: TouchEvent) => {
      if (!trackRef.current) return;
      
      const touch = e.touches[0];
      const rect = trackRef.current.getBoundingClientRect();
      const y = touch.clientY - rect.top;
      const percentage = Math.max(0, Math.min(1, y / rect.height));
      const newPosition = percentage * maxScroll;
      
      setIframeScroll(newPosition);
    };
    
    const handleTouchEnd = () => {
      setIsDragging(false);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
    
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  }, [maxScroll, setIframeScroll]);

  // 快速滚动按钮
  const scrollUp = () => {
    const newPosition = Math.max(0, scrollPosition - 100);
    setIframeScroll(newPosition);
  };

  const scrollDown = () => {
    const newPosition = Math.min(maxScroll, scrollPosition + 100);
    setIframeScroll(newPosition);
  };

  // 计算滑块位置
  const thumbPosition = maxScroll > 0 ? (scrollPosition / maxScroll) * 100 : 0;

  if (!isEditMode) return null;

  return (
    <>
      {/* 添加样式到iframe */}
      <style>{`
        .mobile-edit-hover {
          outline: 2px solid #3b82f6 !important;
          outline-offset: 1px !important;
          background-color: rgba(59, 130, 246, 0.1) !important;
          cursor: pointer !important;
        }
        
        .mobile-edit-selected {
          outline: 3px solid #ef4444 !important;
          outline-offset: 2px !important;
          background-color: rgba(239, 68, 68, 0.15) !important;
          position: relative !important;
        }
        
        .mobile-edit-selected::before {
          content: "已选择";
          position: absolute !important;
          top: -28px !important;
          left: 0 !important;
          background: #ef4444 !important;
          color: white !important;
          padding: 4px 8px !important;
          font-size: 12px !important;
          border-radius: 4px !important;
          z-index: 10000 !important;
          font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
        }
      `}</style>

      {/* 左侧滚动控制条 */}
      <div className="absolute left-1 top-12 bottom-20 w-8 z-40 lg:hidden flex flex-col">
        {/* 向上按钮 */}
        <button
          onClick={scrollUp}
          className="w-8 h-8 bg-blue-600/90 hover:bg-blue-700 text-white rounded-t-lg flex items-center justify-center backdrop-blur-sm border border-blue-500/50 active:scale-95 transition-all"
          disabled={scrollPosition <= 0}
        >
          <ChevronUp className="w-4 h-4" />
        </button>

        {/* 滚动轨道 */}
        <div 
          ref={trackRef}
          className="flex-1 w-8 bg-gray-800/80 backdrop-blur-sm border-x border-gray-600/50 relative cursor-pointer"
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
        >
          {/* 滚动滑块 */}
          <div
            ref={thumbRef}
            className={`absolute left-0.5 right-0.5 bg-blue-500 rounded-full transition-all duration-150 ${
              isDragging ? 'bg-blue-400 shadow-lg' : 'bg-blue-500'
            }`}
            style={{
              top: `${thumbPosition}%`,
              height: '32px',
              transform: 'translateY(-50%)',
            }}
          >
            <div className="w-full h-full rounded-full bg-white/20"></div>
          </div>
          
          {/* 滚动进度指示器 - 简化显示 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white/50 text-[10px] font-medium transform rotate-90 origin-center">
              {Math.round(thumbPosition)}%
            </div>
          </div>
        </div>

        {/* 向下按钮 */}
        <button
          onClick={scrollDown}
          className="w-8 h-8 bg-blue-600/90 hover:bg-blue-700 text-white rounded-b-lg flex items-center justify-center backdrop-blur-sm border border-blue-500/50 active:scale-95 transition-all"
          disabled={scrollPosition >= maxScroll}
        >
          <ChevronDown className="w-4 h-4" />
        </button>
      </div>

      {/* 当前元素信息提示 */}
      {currentElementInfo && (
        <div className="absolute top-16 left-12 right-4 z-50 lg:hidden">
          <div className="bg-blue-600/95 backdrop-blur-sm text-white px-3 py-2 rounded-lg shadow-lg flex items-center gap-2 max-w-sm">
            <Target className="w-3 h-3 flex-shrink-0" />
            <div className="min-w-0">
              <div className="font-medium text-xs">
                {currentElementInfo.tagName.toUpperCase()}
              </div>
              {currentElementInfo.text && (
                <div className="text-[10px] text-blue-100 truncate">
                  {currentElementInfo.text}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 编辑模式状态栏 */}
      <div className="absolute bottom-4 left-4 right-4 z-50 lg:hidden">
        <div className="bg-green-600/95 backdrop-blur-sm text-white px-4 py-3 rounded-xl shadow-lg text-center">
          <div className="flex items-center justify-center gap-2">
            <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
            <span className="font-medium text-sm">编辑模式已激活</span>
          </div>
          <div className="text-xs text-green-100 mt-1">
            点击任意元素进行编辑，左侧滑动条控制滚动
          </div>
        </div>
      </div>
    </>
  );
}; 