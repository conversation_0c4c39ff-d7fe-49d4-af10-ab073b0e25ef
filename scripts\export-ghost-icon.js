const { createCanvas } = require('canvas');
const fs = require('fs');
const path = require('path');

// 小精灵图标的颜色配置 - 与React组件完全一致
const colorModes = {
  dark: {
    mainGradient: ['#FFB3BA', '#BAFFC9', '#BAE1FF'],
    shadowColor: 'rgba(255, 179, 186, 0.4)',
    rightDecoration: ['#FF8A95', '#85E3FF'],
    leftDecoration: ['#85E3FF', '#A8E6CF']
  },
  light: {
    mainGradient: ['#8B9FFF', '#A78BFA', '#F8BBD9'],
    shadowColor: 'rgba(139, 159, 255, 0.25)',
    rightDecoration: ['#FF9EC7', '#7DD3FC'],
    leftDecoration: ['#67E8F9', '#8B9FFF']
  }
};

// 创建渐变色 - 修正135度角度计算
function createGradient(ctx, colors, x, y, width, height, angle = 135) {
  // 135度渐变：从左上角到右下角
  const x1 = x;
  const y1 = y;
  const x2 = x + width;
  const y2 = y + height;

  const gradient = ctx.createLinearGradient(x1, y1, x2, y2);

  if (colors.length === 2) {
    gradient.addColorStop(0, colors[0]);
    gradient.addColorStop(1, colors[1]);
  } else if (colors.length === 3) {
    // 三色渐变：粉色 -> 绿色 -> 蓝色，调整分布比例
    gradient.addColorStop(0, colors[0]);    // #FFB3BA 粉色
    gradient.addColorStop(0.6, colors[1]);  // #BAFFC9 绿色
    gradient.addColorStop(1, colors[2]);    // #BAE1FF 蓝色
  }

  return gradient;
}

// 绘制小精灵图标
function drawGhostLogo(ctx, size, x = 0, y = 0, colors) {
  const centerX = x + size / 2;
  const centerY = y + size / 2;
  
  // 保存当前状态
  ctx.save();
  
  // 主体尺寸
  const bodyWidth = size * 0.9;
  const bodyHeight = size;
  const bodyX = centerX - bodyWidth / 2;
  const bodyY = centerY - bodyHeight / 2 + size * 0.05;
  
  // 绘制主体
  ctx.save();
  ctx.translate(centerX, centerY);
  ctx.rotate(-2 * Math.PI / 180); // -2度旋转
  ctx.translate(-centerX, -centerY);
  
  // 主体路径
  ctx.beginPath();
  const radius1 = size * 0.45;
  const radius2 = size * 0.35;
  const radius3 = size * 0.3;
  
  // 创建圆角矩形路径
  ctx.moveTo(bodyX + radius1, bodyY);
  ctx.lineTo(bodyX + bodyWidth - radius1, bodyY);
  ctx.quadraticCurveTo(bodyX + bodyWidth, bodyY, bodyX + bodyWidth, bodyY + radius1);
  ctx.lineTo(bodyX + bodyWidth, bodyY + bodyHeight - radius2);
  ctx.quadraticCurveTo(bodyX + bodyWidth, bodyY + bodyHeight, bodyX + bodyWidth - radius2, bodyY + bodyHeight);
  ctx.lineTo(bodyX + radius3, bodyY + bodyHeight);
  ctx.quadraticCurveTo(bodyX, bodyY + bodyHeight, bodyX, bodyY + bodyHeight - radius3);
  ctx.lineTo(bodyX, bodyY + radius1);
  ctx.quadraticCurveTo(bodyX, bodyY, bodyX + radius1, bodyY);
  ctx.closePath();
  
  // 填充主体渐变
  const mainGradient = createGradient(ctx, colors.mainGradient, bodyX, bodyY, bodyWidth, bodyHeight);
  ctx.fillStyle = mainGradient;
  ctx.fill();

  // 添加阴影效果
  ctx.shadowColor = colors.shadowColor;
  ctx.shadowBlur = size * 0.2;
  ctx.shadowOffsetY = size * 0.08;
  ctx.fill();
  
  ctx.restore();
  
  // 绘制右侧装饰
  ctx.save();
  const rightDecorX = bodyX + bodyWidth - size * 0.06;
  const rightDecorY = bodyY + size * 0.12;
  const rightDecorWidth = size * 0.2;
  const rightDecorHeight = size * 0.25;
  
  ctx.translate(rightDecorX + rightDecorWidth/2, rightDecorY + rightDecorHeight/2);
  ctx.rotate(15 * Math.PI / 180);
  ctx.translate(-rightDecorWidth/2, -rightDecorHeight/2);
  
  ctx.beginPath();
  ctx.ellipse(rightDecorWidth/2, rightDecorHeight/2, rightDecorWidth/2, rightDecorHeight/2, 0, 0, 2 * Math.PI);
  
  const rightGradient = createGradient(ctx, colors.rightDecoration, 0, 0, rightDecorWidth, rightDecorHeight);
  ctx.fillStyle = rightGradient;
  ctx.fill();
  ctx.restore();
  
  // 绘制左侧装饰
  ctx.save();
  const leftDecorX = bodyX - size * 0.06;
  const leftDecorY = bodyY + bodyHeight - size * 0.12 - size * 0.25;
  const leftDecorWidth = size * 0.2;
  const leftDecorHeight = size * 0.25;
  
  ctx.translate(leftDecorX + leftDecorWidth/2, leftDecorY + leftDecorHeight/2);
  ctx.rotate(-165 * Math.PI / 180);
  ctx.translate(-leftDecorWidth/2, -leftDecorHeight/2);
  
  ctx.beginPath();
  ctx.ellipse(leftDecorWidth/2, leftDecorHeight/2, leftDecorWidth/2, leftDecorHeight/2, 0, 0, 2 * Math.PI);
  
  const leftGradient = createGradient(ctx, colors.leftDecoration, 0, 0, leftDecorWidth, leftDecorHeight);
  ctx.fillStyle = leftGradient;
  ctx.fill();
  ctx.restore();
  
  // 绘制眼睛
  const eyeWidth = size * 0.11;
  const eyeHeight = size * 0.16;
  const eyeY = bodyY + size * 0.3;
  const eyeGap = size * 0.1;
  const leftEyeX = centerX - eyeGap/2 - eyeWidth;
  const rightEyeX = centerX + eyeGap/2;
  
  // 左眼
  ctx.beginPath();
  ctx.ellipse(leftEyeX + eyeWidth/2, eyeY + eyeHeight/2, eyeWidth/2, eyeHeight/2, 0, 0, 2 * Math.PI);
  ctx.fillStyle = '#1F2937';
  ctx.shadowColor = 'rgba(0,0,0,0.1)';
  ctx.shadowBlur = 2;
  ctx.shadowOffsetY = 1;
  ctx.fill();
  
  // 右眼
  ctx.beginPath();
  ctx.ellipse(rightEyeX + eyeWidth/2, eyeY + eyeHeight/2, eyeWidth/2, eyeHeight/2, 0, 0, 2 * Math.PI);
  ctx.fill();
  
  // 恢复状态
  ctx.restore();
}

// 导出不同尺寸的PNG
function exportGhostIcon() {
  const sizes = [16, 32, 48, 64, 128, 256, 512];
  const baseOutputDir = path.join(__dirname, '../public/ghost-icons');

  // 创建基础输出目录
  if (!fs.existsSync(baseOutputDir)) {
    fs.mkdirSync(baseOutputDir, { recursive: true });
  }

  // 导出两种模式
  Object.keys(colorModes).forEach(mode => {
    const modeDir = path.join(baseOutputDir, mode);

    // 创建模式目录
    if (!fs.existsSync(modeDir)) {
      fs.mkdirSync(modeDir, { recursive: true });
    }

    console.log(`\n🎨 导出${mode === 'dark' ? '深色' : '浅色'}模式图标...`);

    sizes.forEach(size => {
      // 创建画布
      const canvas = createCanvas(size, size);
      const ctx = canvas.getContext('2d');

      // 设置透明背景
      ctx.clearRect(0, 0, size, size);

      // 绘制小精灵 - 缩小7%避免右侧装饰被截断
      drawGhostLogo(ctx, size * 0.83, size * 0.085, size * 0.085, colorModes[mode]);

      // 保存为PNG
      const buffer = canvas.toBuffer('image/png');
      const filename = `ghost-icon-${mode}-${size}x${size}.png`;
      const filepath = path.join(modeDir, filename);

      fs.writeFileSync(filepath, buffer);
      console.log(`✅ 已导出: ${mode}/${filename}`);
    });
  });

  console.log(`\n🎉 所有图标已导出到: ${baseOutputDir}`);
  console.log(`📁 深色模式: ${path.join(baseOutputDir, 'dark')}`);
  console.log(`📁 浅色模式: ${path.join(baseOutputDir, 'light')}`);
}

// 运行导出
if (require.main === module) {
  try {
    exportGhostIcon();
  } catch (error) {
    console.error('❌ 导出失败:', error.message);
    console.log('\n💡 请确保已安装canvas依赖:');
    console.log('npm install canvas');
  }
}

module.exports = { exportGhostIcon, drawGhostLogo };
