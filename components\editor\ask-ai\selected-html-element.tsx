import classNames from "classnames";
import { Code, XCircle } from "lucide-react";

import { Collapsible, CollapsibleTrigger } from "@/components/ui/collapsible";

export const SelectedHtmlElement = ({
  element,
  isAiWorking = false,
  onDelete,
}: {
  element: HTMLElement | null;
  isAiWorking: boolean;
  onDelete?: () => void;
}) => {
  if (!element) return null;

  const tagName = element.tagName.toLowerCase();
  const textContent = element.textContent?.trim() || "";
  const hasText = textContent.length > 0;
  
  // 分析元素类型
  const getElementType = (el: HTMLElement): string => {
    const tag = el.tagName.toLowerCase();
    if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tag)) return '标题';
    if (tag === 'p') return '段落';
    if (tag === 'button') return '按钮';
    if (tag === 'a') return '链接';
    if (tag === 'img') return '图片';
    if (tag === 'input') return '输入框';
    if (tag === 'div' && hasText) return '文本';
    if (tag === 'span' && hasText) return '文本';
    if (tag === 'td' || tag === 'th') return '表格单元';
    return '元素';
  };
  
  const elementType = getElementType(element);
  const previewText = textContent.length > 20 ? textContent.substring(0, 20) + '...' : textContent;
  
  return (
    <Collapsible
      className={classNames(
        "selected-element-tag element-tag-animation bg-gradient-to-r from-blue-500/15 to-purple-500/15 backdrop-blur-sm border border-blue-500/40 rounded-lg shadow-md shadow-blue-500/10 max-w-max transition-all duration-300 ease-out group",
        "hover:from-blue-500/25 hover:to-purple-500/25 hover:border-blue-400/60 hover:shadow-blue-500/20 hover:scale-102",
        {
          "cursor-pointer": !isAiWorking,
          "opacity-50 cursor-not-allowed": isAiWorking,
        }
      )}
      disabled={isAiWorking}
      onClick={() => {
        if (!isAiWorking && onDelete) {
          onDelete();
        }
      }}
    >
      <CollapsibleTrigger className="flex items-center justify-start gap-1.5 px-1.5 py-1 cursor-pointer max-w-80">
        <div className="rounded bg-gradient-to-br from-blue-600 to-purple-600 size-4 flex items-center justify-center shadow-sm group-hover:shadow-md transition-all duration-200 flex-shrink-0">
          <Code className="text-white size-2.5 drop-shadow-sm" />
        </div>
        <div className="flex flex-col items-start min-w-0 flex-1">
          <p className="text-xs font-medium text-white leading-none">
            {elementType} ({tagName})
          </p>
          {hasText && (
            <p className="text-[10px] text-blue-200 leading-none mt-0.5 truncate max-w-full">
              {`"${previewText}"`}
            </p>
          )}
        </div>
        <div className="opacity-50 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0">
          <XCircle className="text-blue-200 size-2.5 hover:text-white transition-colors duration-200" />
        </div>
      </CollapsibleTrigger>
      {/* <CollapsibleContent className="border-t border-neutral-700 pt-2 mt-2">
        <div className="text-xs text-neutral-400">
          <p>
            <span className="font-semibold">ID:</span> {element.id || "No ID"}
          </p>
          <p>
            <span className="font-semibold">Classes:</span>{" "}
            {element.className || "No classes"}
          </p>
        </div>
      </CollapsibleContent> */}
    </Collapsible>
  );
};
