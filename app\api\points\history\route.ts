import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;

    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '20', 10), 100); // 限制最大100条
    const offset = parseInt(searchParams.get('offset') || '0', 10);
    const transactionType = searchParams.get('type'); // 'earn' | 'spend' | null
    const pointsType = searchParams.get('points_type'); // 'activity' | 'subscription' | 'recharge' | null
    const sourceType = searchParams.get('source_type'); // 具体来源类型
    const dateFrom = searchParams.get('date_from'); // 开始日期
    const dateTo = searchParams.get('date_to'); // 结束日期

    // 构建查询条件（使用表别名避免歧义）
    let whereConditions = ['pt.user_id = ?'];
    let queryParams: (string | number)[] = [user.id];

    if (transactionType && ['earn', 'spend'].includes(transactionType)) {
      whereConditions.push('pt.transaction_type = ?');
      queryParams.push(transactionType);
    }

    if (pointsType && ['activity', 'subscription', 'recharge'].includes(pointsType)) {
      whereConditions.push('pt.points_type = ?');
      queryParams.push(pointsType);
    }

    if (sourceType) {
      whereConditions.push('pt.source_type = ?');
      queryParams.push(sourceType);
    }

    if (dateFrom) {
      whereConditions.push('pt.created_at >= ?');
      queryParams.push(dateFrom);
    }

    if (dateTo) {
      whereConditions.push('pt.created_at <= ?');
      queryParams.push(dateTo + ' 23:59:59');
    }

    const whereClause = whereConditions.join(' AND ');

    // 获取总数（用于分页）
    const countQuery = `SELECT COUNT(*) as total FROM points_transactions pt WHERE ${whereClause}`;
    const countResult = await executeQuery(countQuery, queryParams) as { total: number }[];
    const total = countResult[0]?.total || 0;

    // 获取详细记录
    const historyQuery = `
      SELECT
        pt.id,
        pt.user_id,
        pt.transaction_type,
        pt.points_amount,
        pt.balance_before,
        pt.balance_after,
        pt.source_type,
        pt.points_type,
        pt.expires_at,
        pt.balance_record_id,
        pt.source_id,
        pt.description,
        pt.metadata,
        pt.created_at,
        upb.expires_at as balance_expires_at,
        upb.is_active as balance_is_active
      FROM points_transactions pt
      LEFT JOIN user_points_balance upb ON pt.balance_record_id = upb.id
      WHERE ${whereClause}
      ORDER BY pt.created_at DESC, pt.id DESC
      LIMIT ? OFFSET ?
    `;

    // 创建新的参数数组，确保类型正确
    const finalQueryParams: (string | number)[] = [...queryParams, limit, offset];
    const history = await executeQuery(historyQuery, finalQueryParams);

    // 处理数据，添加额外信息
    const processedHistory = history.map((record: any) => {
      // 解析 metadata
      let metadata = null;
      if (record.metadata) {
        try {
          metadata = typeof record.metadata === 'string' ? JSON.parse(record.metadata) : record.metadata;
        } catch (e) {
          metadata = record.metadata;
        }
      }

      // 判断积分是否已过期
      const isExpired = record.expires_at ? new Date(record.expires_at) < new Date() : false;

      // 判断是否即将过期（7天内）
      const isExpiringSoon = record.expires_at ?
        (new Date(record.expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) <= 7 &&
        (new Date(record.expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24) > 0 : false;

      return {
        ...record,
        metadata,
        isExpired,
        isExpiringSoon,
        // 格式化显示用的字段
        formattedAmount: record.transaction_type === 'earn' ? `+${record.points_amount}` : `-${record.points_amount}`,
        formattedDate: new Date(record.created_at).toLocaleString('zh-CN'),
        formattedExpiry: record.expires_at ? new Date(record.expires_at).toLocaleDateString('zh-CN') : null
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        history: processedHistory,
        pagination: {
          limit,
          offset,
          total,
          hasMore: offset + limit < total,
          currentPage: Math.floor(offset / limit) + 1,
          totalPages: Math.ceil(total / limit)
        },
        filters: {
          transactionType,
          pointsType,
          sourceType,
          dateFrom,
          dateTo
        }
      }
    });

  } catch (error) {
    console.error("获取积分历史失败:", error);
    return NextResponse.json({ error: "服务器错误" }, { status: 500 });
  }
}
