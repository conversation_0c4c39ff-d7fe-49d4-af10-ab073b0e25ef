const mysql = require('mysql2/promise');

async function createRegistrationLogsTable() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'loomrun',
      charset: 'utf8mb4'
    });

    console.log('🔗 数据库连接成功');

    // 检查表是否存在
    const [tables] = await connection.execute('SHOW TABLES LIKE "registration_logs"');
    console.log('registration_logs表存在:', tables.length > 0);
    
    if (tables.length === 0) {
      console.log('创建registration_logs表...');
      await connection.execute(`
        CREATE TABLE registration_logs (
          id int NOT NULL AUTO_INCREMENT,
          user_id int NOT NULL,
          registration_type enum('phone','wechat') NOT NULL,
          registration_source varchar(100) DEFAULT NULL,
          invite_code varchar(20) DEFAULT NULL,
          inviter_user_id int DEFAULT NULL,
          points_awarded int NOT NULL DEFAULT 0,
          ip_address varchar(45) DEFAULT NULL,
          user_agent text DEFAULT NULL,
          metadata json DEFAULT NULL,
          created_at timestamp DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          KEY idx_user_id (user_id),
          KEY idx_registration_type (registration_type),
          KEY idx_created_at (created_at),
          CONSTRAINT registration_logs_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      console.log('✅ registration_logs表创建成功');
    } else {
      console.log('✅ registration_logs表已存在');
    }

  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔚 数据库连接已关闭');
    }
  }
}

createRegistrationLogsTable().catch(console.error);
