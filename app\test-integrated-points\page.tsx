"use client";

import { useUser } from "@/loomrunhooks/useUser";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Header } from "@/components/editor/header";
import { Coins, ExternalLink, User, CheckCircle } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function TestIntegratedPointsPage() {
  const { user } = useUser();
  const router = useRouter();

  return (
    <div className="h-screen bg-background flex flex-col">
      {/* 使用系统的 Header 组件 */}
      <Header
        onLogoClick={() => router.push('/')}
      >
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2 text-sm"
        >
          返回
        </Button>
      </Header>
      
      {/* 主内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="flex items-center gap-2 mb-6">
            <Coins className="w-6 h-6" />
            <h1 className="text-2xl font-bold">集成版积分页面测试</h1>
          </div>

          {/* 用户状态 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                用户状态
              </CardTitle>
              <CardDescription>当前登录状态和积分信息</CardDescription>
            </CardHeader>
            <CardContent>
              {user ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{user.points || 0}</div>
                      <div className="text-sm text-muted-foreground">当前积分</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{user.total_earned_points || 0}</div>
                      <div className="text-sm text-muted-foreground">累计获得</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{user.total_spent_points || 0}</div>
                      <div className="text-sm text-muted-foreground">累计消费</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="text-lg font-bold text-purple-600">{user.nickname || user.name}</div>
                      <div className="text-sm text-muted-foreground">用户昵称</div>
                    </div>
                  </div>
                  
                  <Link href="/points-integrated">
                    <Button className="flex items-center gap-2">
                      <Coins className="w-4 h-4" />
                      查看集成版积分详情
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">请先登录以查看积分信息</p>
                  <Link href="/">
                    <Button>前往首页登录</Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 集成特性说明 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>集成版积分页面特性</CardTitle>
              <CardDescription>与系统完美集成的积分详情页面</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-600 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    系统集成特性
                  </h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2"></div>
                      <span>继承系统顶部栏（Header）</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2"></div>
                      <span>保持系统一致的视觉风格</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2"></div>
                      <span>支持深色和浅色模式</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2"></div>
                      <span>响应式布局设计</span>
                    </li>
                  </ul>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-blue-600 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    功能特性
                  </h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
                      <span>积分概览卡片</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
                      <span>积分变动历史</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
                      <span>积分类型标识</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
                      <span>有效期显示</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 访问方式 */}
          <Card>
            <CardHeader>
              <CardTitle>访问方式</CardTitle>
              <CardDescription>如何访问集成版积分详情页面</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <h5 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">推荐方式</h5>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    点击系统顶部用户菜单中的积分卡片"详情"按钮，会自动跳转到集成版积分页面
                  </p>
                </div>
                
                <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <h5 className="font-semibold text-green-800 dark:text-green-200 mb-2">直接访问</h5>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    直接访问 <code className="bg-muted px-1 rounded">/points-integrated</code> 路径
                  </p>
                </div>
                
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                  <h5 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">特点</h5>
                  <p className="text-sm text-purple-700 dark:text-purple-300">
                    页面会自动检查登录状态，未登录用户会被重定向到首页
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 测试按钮 */}
          <div className="mt-8 flex gap-4 flex-wrap">
            <Link href="/points-integrated">
              <Button className="flex items-center gap-2">
                <Coins className="w-4 h-4" />
                集成版积分页面
              </Button>
            </Link>
            
            <Link href="/points">
              <Button variant="outline" className="flex items-center gap-2">
                <Coins className="w-4 h-4" />
                独立版积分页面
              </Button>
            </Link>
            
            <Link href="/points-simple">
              <Button variant="outline" className="flex items-center gap-2">
                <Coins className="w-4 h-4" />
                简化版积分页面
              </Button>
            </Link>
            
            <Link href="/test-components">
              <Button variant="ghost" className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                组件测试页面
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
