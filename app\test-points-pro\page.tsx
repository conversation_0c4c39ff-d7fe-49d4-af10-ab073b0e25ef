"use client";

import { useUser } from "@/loomrunhooks/useUser";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Header } from "@/components/editor/header";
import { Coins, ExternalLink, User, CheckCircle, Layout, Sidebar, Monitor } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function TestPointsProPage() {
  const { user } = useUser();
  const router = useRouter();

  return (
    <div className="h-screen bg-background flex flex-col">
      {/* 使用系统的 Header 组件 */}
      <Header onLogoClick={() => router.push('/')} />
      
      {/* 主内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          <div className="flex items-center gap-2 mb-6">
            <Coins className="w-6 h-6" />
            <h1 className="text-2xl font-bold">专业版积分页面测试</h1>
          </div>

          {/* 用户状态 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                用户状态
              </CardTitle>
              <CardDescription>当前登录状态和积分信息</CardDescription>
            </CardHeader>
            <CardContent>
              {user ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{user.points || 0}</div>
                      <div className="text-sm text-muted-foreground">当前积分</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{user.total_earned_points || 0}</div>
                      <div className="text-sm text-muted-foreground">累计获得</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{user.total_spent_points || 0}</div>
                      <div className="text-sm text-muted-foreground">累计消费</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="text-lg font-bold text-purple-600">{user.nickname || user.name}</div>
                      <div className="text-sm text-muted-foreground">用户昵称</div>
                    </div>
                  </div>
                  
                  <Link href="/points-pro">
                    <Button className="flex items-center gap-2">
                      <Coins className="w-4 h-4" />
                      查看专业版积分详情
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">请先登录以查看积分信息</p>
                  <Link href="/">
                    <Button>前往首页登录</Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 专业版特性说明 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layout className="w-5 h-5" />
                专业版积分页面特性
              </CardTitle>
              <CardDescription>成熟的左侧工具栏 + 右侧内容区布局设计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-blue-600 flex items-center gap-2">
                    <Sidebar className="w-4 h-4" />
                    左侧工具栏特性
                  </h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>积分概览卡片（当前积分、累计获得、累计消费）</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>记录筛选（全部、获得、消费）</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>快捷操作（刷新、充值、邀请）</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>固定宽度320px，充分利用空间</span>
                    </li>
                  </ul>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-600 flex items-center gap-2">
                    <Monitor className="w-4 h-4" />
                    右侧内容区特性
                  </h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>积分变动历史详细展示</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>动态记录数量显示</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>根据筛选条件动态更新内容</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>充分利用剩余屏幕空间</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 设计理念 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>设计理念</CardTitle>
              <CardDescription>符合现代系统的成熟布局设计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <h5 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">🎯 高页面利用率</h5>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    左侧工具栏固定320px宽度，右侧内容区占据剩余空间，充分利用屏幕面积，避免中间居中布局的空间浪费
                  </p>
                </div>
                
                <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <h5 className="font-semibold text-green-800 dark:text-green-200 mb-2">🎨 成熟的视觉设计</h5>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    采用现代应用常见的侧边栏布局，左侧功能区域，右侧内容展示，符合用户使用习惯
                  </p>
                </div>
                
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                  <h5 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">⚡ 功能集中化</h5>
                  <p className="text-sm text-purple-700 dark:text-purple-300">
                    所有积分相关的操作和信息都集中在左侧工具栏，用户可以快速访问各种功能，提高操作效率
                  </p>
                </div>
                
                <div className="p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
                  <h5 className="font-semibold text-orange-800 dark:text-orange-200 mb-2">🔄 动态交互</h5>
                  <p className="text-sm text-orange-700 dark:text-orange-300">
                    左侧筛选条件会实时影响右侧内容显示，提供流畅的交互体验，记录数量动态更新
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 访问方式 */}
          <Card>
            <CardHeader>
              <CardTitle>访问方式</CardTitle>
              <CardDescription>如何访问专业版积分详情页面</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <h5 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">推荐方式</h5>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    点击系统顶部用户菜单中的积分卡片"详情"按钮，会自动跳转到专业版积分页面
                  </p>
                </div>
                
                <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <h5 className="font-semibold text-green-800 dark:text-green-200 mb-2">直接访问</h5>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    直接访问 <code className="bg-muted px-1 rounded">/points-pro</code> 路径
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 测试按钮 */}
          <div className="mt-8 flex gap-4 flex-wrap">
            <Link href="/points-pro">
              <Button className="flex items-center gap-2">
                <Coins className="w-4 h-4" />
                专业版积分页面
              </Button>
            </Link>
            
            <Link href="/points-integrated">
              <Button variant="outline" className="flex items-center gap-2">
                <Coins className="w-4 h-4" />
                集成版积分页面
              </Button>
            </Link>
            
            <Link href="/points">
              <Button variant="outline" className="flex items-center gap-2">
                <Coins className="w-4 h-4" />
                标准版积分页面
              </Button>
            </Link>
            
            <Link href="/test-components">
              <Button variant="ghost" className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                组件测试页面
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
