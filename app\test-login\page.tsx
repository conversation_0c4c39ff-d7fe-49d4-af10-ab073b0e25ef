"use client";

import { useState } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AuthModal } from "@/components/auth-modal";
import { LogIn, User, RefreshCw } from "lucide-react";

export default function TestLoginPage() {
  const { user, refreshUser, logout } = useUser();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const handleRefreshUser = async () => {
    setRefreshing(true);
    try {
      await refreshUser();
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoginSuccess = () => {
    console.log('✅ 登录成功回调被调用');
    setAuthModalOpen(false);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2">
        <User className="w-6 h-6" />
        <h1 className="text-2xl font-bold">登录流程测试</h1>
      </div>

      {/* 用户状态显示 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            用户状态
          </CardTitle>
          <CardDescription>当前登录状态和用户信息</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {user ? (
            <div className="space-y-3">
              <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex items-center gap-2 text-green-800 dark:text-green-200">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="font-medium">已登录</span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">用户ID</label>
                  <div className="text-lg font-mono">{user.id}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">昵称</label>
                  <div className="text-lg">{user.nickname || user.name || '未设置'}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">手机号</label>
                  <div className="text-lg">{user.phone || '未绑定'}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">积分</label>
                  <div className="text-lg font-bold text-blue-600">{user.points || 0}</div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={handleRefreshUser} 
                  disabled={refreshing}
                  variant="outline"
                  size="sm"
                >
                  {refreshing && <RefreshCw className="w-4 h-4 mr-2 animate-spin" />}
                  刷新用户信息
                </Button>
                <Button 
                  onClick={logout} 
                  variant="destructive"
                  size="sm"
                >
                  登出
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center gap-2 text-red-800 dark:text-red-200">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="font-medium">未登录</span>
                </div>
              </div>
              
              <Button 
                onClick={() => setAuthModalOpen(true)}
                className="flex items-center gap-2"
              >
                <LogIn className="w-4 h-4" />
                打开登录弹窗
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 测试说明 */}
      <Card>
        <CardHeader>
          <CardTitle>测试说明</CardTitle>
          <CardDescription>验证登录流程是否正常工作</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
              <span>点击"打开登录弹窗"按钮</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
              <span>输入手机号并获取验证码</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
              <span>输入验证码并点击登录</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2"></div>
              <span>登录成功后弹窗应该自动关闭，用户状态应该更新</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2"></div>
              <span>新用户应该自动获得100积分</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 登录弹窗 */}
      <AuthModal
        open={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        onSuccess={handleLoginSuccess}
        title="测试登录"
        description="测试登录流程是否正常工作"
      />
    </div>
  );
}
