require('dotenv').config();
const { sendSmsCode, validatePhoneNumber, generateSmsCode } = require('../lib/sms-service.js');

async function testSmsService() {
  console.log('🧪 开始测试阿里云短信服务...\n');

  // 检查环境变量
  console.log('📋 检查环境变量配置:');
  const requiredEnvs = [
    'ALIYUN_ACCESS_KEY_ID',
    'ALIYUN_ACCESS_KEY_SECRET',
    'ALIYUN_SMS_SIGN_NAME',
    'ALIYUN_SMS_TEMPLATE_CODE'
  ];

  let configValid = true;
  requiredEnvs.forEach(env => {
    const value = process.env[env];
    if (!value || value === 'your_access_key_id' || value === 'your_access_key_secret') {
      console.log(`❌ ${env}: 未配置或使用默认值`);
      configValid = false;
    } else {
      console.log(`✅ ${env}: ${env.includes('SECRET') ? '***已配置***' : value}`);
    }
  });

  if (!configValid) {
    console.log('\n❌ 环境变量配置不完整，请参考 docs/sms-setup.md 进行配置');
    return;
  }

  console.log('\n✅ 环境变量配置检查通过\n');

  // 测试手机号验证
  console.log('📱 测试手机号验证:');
  const testPhones = [
    '13800138000',  // 有效
    '19855171827',  // 有效
    '12345678901',  // 无效
    '138001380001', // 无效
  ];

  testPhones.forEach(phone => {
    const isValid = validatePhoneNumber(phone);
    console.log(`${isValid ? '✅' : '❌'} ${phone}: ${isValid ? '有效' : '无效'}`);
  });

  // 测试验证码生成
  console.log('\n🔢 测试验证码生成:');
  for (let i = 0; i < 3; i++) {
    const code = generateSmsCode();
    console.log(`✅ 生成验证码: ${code}`);
  }

  // 询问是否发送测试短信
  console.log('\n📤 是否发送测试短信？');
  console.log('请在命令行中输入测试手机号（输入 q 退出）:');
  
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  rl.question('手机号: ', async (phone) => {
    if (phone.toLowerCase() === 'q') {
      console.log('👋 测试结束');
      rl.close();
      return;
    }

    if (!validatePhoneNumber(phone)) {
      console.log('❌ 手机号格式不正确');
      rl.close();
      return;
    }

    console.log(`📤 正在发送测试短信到 ${phone}...`);
    
    try {
      const code = generateSmsCode();
      const result = await sendSmsCode(phone, code);
      
      if (result.success) {
        console.log(`✅ 短信发送成功！`);
        console.log(`📱 验证码: ${code}`);
        console.log(`🆔 BizId: ${result.bizId || 'N/A'}`);
      } else {
        console.log(`❌ 短信发送失败: ${result.message}`);
      }
    } catch (error) {
      console.log(`❌ 发送过程中出错: ${error.message}`);
    }
    
    rl.close();
  });
}

// 运行测试
testSmsService().catch(console.error);
