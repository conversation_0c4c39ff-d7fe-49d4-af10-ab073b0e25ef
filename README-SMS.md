# 手机验证码登录功能实现

## 功能概述

本项目已集成阿里云短信服务，实现了真实的手机验证码注册登录功能。

## 主要特性

✅ **真实短信发送** - 集成阿里云短信服务API  
✅ **验证码验证** - 5分钟有效期，使用后自动失效  
✅ **频率限制** - 1分钟内不能重复发送  
✅ **手机号验证** - 严格的中国大陆手机号格式验证  
✅ **自动注册** - 新手机号自动创建用户账号  
✅ **JWT认证** - 登录后生成JWT Token  

## 快速开始

### 1. 配置阿里云短信服务

请参考 `docs/sms-setup.md` 完成阿里云短信服务的配置。

### 2. 配置环境变量

在 `.env` 文件中添加您的阿里云配置：

```env
ALIYUN_ACCESS_KEY_ID=your_actual_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_actual_access_key_secret
ALIYUN_SMS_SIGN_NAME=万来云边
ALIYUN_SMS_TEMPLATE_CODE=SMS_324480375
```

### 3. 验证配置

运行配置验证脚本：

```bash
npm run verify:config
```

### 4. 测试短信服务

运行测试脚本验证短信发送：

```bash
npm run test:sms
```

### 5. 启动项目

```bash
npm run dev
```

## API 接口

### 发送验证码

```http
POST /api/auth/send-code
Content-Type: application/json

{
  "phone": "13800138000"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "验证码发送成功"
}
```

### 手机号登录

```http
POST /api/auth/login
Content-Type: application/json

{
  "type": "phone",
  "phone": "13800138000",
  "code": "123456"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "登录成功",
  "user": {
    "id": 1,
    "phone": "13800138000",
    "nickname": "用户8000",
    "created_at": "2025-07-29T00:00:00.000Z"
  }
}
```

## 核心文件说明

### `lib/sms-service.ts`
阿里云短信服务封装，包含：
- `sendSmsCode()` - 发送短信验证码
- `validatePhoneNumber()` - 验证手机号格式
- `generateSmsCode()` - 生成6位验证码

### `lib/auth-service.ts`
认证服务，包含：
- `sendVerificationCode()` - 发送验证码（含频率限制）
- `verifyPhoneCode()` - 验证验证码
- `loginWithPhone()` - 手机号登录

### `app/api/auth/send-code/route.ts`
发送验证码API端点

### `app/api/auth/login/route.ts`
登录API端点（支持手机号和微信登录）

## 数据库表结构

### `phone_verifications` 表
```sql
CREATE TABLE phone_verifications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  phone VARCHAR(20) NOT NULL,
  code VARCHAR(6) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  is_used BOOLEAN DEFAULT FALSE,
  INDEX idx_phone_code (phone, code),
  INDEX idx_expires (expires_at)
);
```

### `users` 表
```sql
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  phone VARCHAR(20) UNIQUE,
  wechat_openid VARCHAR(100) UNIQUE,
  nickname VARCHAR(100),
  avatar_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL,
  is_active BOOLEAN DEFAULT TRUE
);
```

## 安全特性

1. **验证码有效期** - 5分钟自动过期
2. **一次性使用** - 验证成功后立即标记为已使用
3. **频率限制** - 同一手机号1分钟内只能发送一次
4. **格式验证** - 严格的手机号格式检查
5. **JWT Token** - 安全的用户认证机制

## 费用说明

- 国内短信费用约 0.045元/条
- 建议在生产环境设置更严格的频率限制
- 可在阿里云控制台监控短信使用量和费用

## 故障排除

如果遇到问题，请检查：

1. **环境变量配置** - 确保所有必需的环境变量都已正确设置
2. **阿里云配置** - 确认AccessKey、签名、模板都已正确配置
3. **网络连接** - 确保服务器能访问阿里云API
4. **账户余额** - 确保阿里云账户有足够余额
5. **模板审核** - 确保短信模板已通过审核

运行 `npm run test:sms` 可以帮助诊断配置问题。
