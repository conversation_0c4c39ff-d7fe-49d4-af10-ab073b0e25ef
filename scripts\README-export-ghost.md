# 🎨 小精灵图标导出工具

这个脚本可以将我们的小精灵React组件导出为PNG格式的图标文件，同时支持浅色和深色两种模式，适用于favicon、应用图标等用途。

## 🚀 使用方法

### 1. 安装依赖
```bash
npm install
```

### 2. 运行导出脚本
```bash
npm run export:ghost
```

### 3. 或者直接运行
```bash
node scripts/export-ghost-icon.js
```

## 📁 输出文件

脚本会在 `public/ghost-icons/` 目录下生成两个子目录，每个目录包含以下尺寸的PNG文件：

### 🌙 深色模式 (`dark/` 目录)
- `ghost-icon-dark-16x16.png` - 浏览器标签页小图标
- `ghost-icon-dark-32x32.png` - 浏览器标签页标准图标
- `ghost-icon-dark-48x48.png` - 浏览器书签图标
- `ghost-icon-dark-64x64.png` - 高分辨率标签页图标
- `ghost-icon-dark-128x128.png` - 应用图标
- `ghost-icon-dark-256x256.png` - 高分辨率应用图标
- `ghost-icon-dark-512x512.png` - PWA应用图标

### ☀️ 浅色模式 (`light/` 目录)
- `ghost-icon-light-16x16.png` - 浏览器标签页小图标
- `ghost-icon-light-32x32.png` - 浏览器标签页标准图标
- `ghost-icon-light-48x48.png` - 浏览器书签图标
- `ghost-icon-light-64x64.png` - 高分辨率标签页图标
- `ghost-icon-light-128x128.png` - 应用图标
- `ghost-icon-light-256x256.png` - 高分辨率应用图标
- `ghost-icon-light-512x512.png` - PWA应用图标

## 🎨 图标特性

- ✅ **双模式支持**：同时导出浅色和深色两种模式
- ✅ **颜色完全一致**：与React组件的浅色/深色模式颜色完全匹配
- ✅ **透明背景**：适合在任何背景上使用
- ✅ **高质量渲染**：矢量转位图，保证清晰度
- ✅ **多尺寸支持**：覆盖所有常用尺寸需求
- ✅ **完整细节**：包含眼睛、装饰元素等所有细节
- ✅ **优化尺寸**：缩小7%确保装饰元素不被截断
- ✅ **自动分类**：按模式自动分类到不同目录

## 🔧 技术实现

- **Canvas API**：使用Node.js Canvas进行服务端渲染
- **渐变绘制**：精确还原React组件中的135度三色渐变效果
- **几何变换**：支持旋转、缩放等变换效果
- **阴影效果**：包含投影和模糊效果
- **颜色校正**：确保与深色模式React组件颜色完全一致

## 📝 使用场景

生成的PNG图标可以用于：

1. **浏览器Favicon（支持深色模式）**
   ```html
   <!-- 默认图标 -->
   <link rel="icon" type="image/png" sizes="32x32" href="/ghost-icons/light/ghost-icon-light-32x32.png">
   <link rel="icon" type="image/png" sizes="16x16" href="/ghost-icons/light/ghost-icon-light-16x16.png">

   <!-- 深色模式图标 -->
   <link rel="icon" type="image/png" sizes="32x32" href="/ghost-icons/dark/ghost-icon-dark-32x32.png" media="(prefers-color-scheme: dark)">
   <link rel="icon" type="image/png" sizes="16x16" href="/ghost-icons/dark/ghost-icon-dark-16x16.png" media="(prefers-color-scheme: dark)">
   ```

2. **PWA应用图标**
   ```json
   {
     "icons": [
       {
         "src": "/ghost-icons/ghost-icon-192x192.png",
         "sizes": "192x192",
         "type": "image/png"
       }
     ]
   }
   ```

3. **移动设备图标**
   ```html
   <link rel="apple-touch-icon" sizes="180x180" href="/ghost-icons/ghost-icon-256x256.png">
   ```

## ⚠️ 注意事项

- 确保已安装 `canvas` 依赖包
- 在某些系统上可能需要安装额外的系统依赖
- 生成的图标为静态图像，不包含动画效果
- 使用的是深色模式配色方案

## 🛠️ 自定义配置

如需修改颜色或尺寸，可以编辑 `scripts/export-ghost-icon.js` 文件中的配置：

```javascript
// 修改颜色配置 - 支持双模式
const colorModes = {
  dark: {
    mainGradient: ['#FFB3BA', '#BAFFC9', '#BAE1FF'],
    shadowColor: 'rgba(255, 179, 186, 0.4)',
    rightDecoration: ['#FF8A95', '#85E3FF'],
    leftDecoration: ['#85E3FF', '#A8E6CF']
  },
  light: {
    mainGradient: ['#8B9FFF', '#A78BFA', '#F8BBD9'],
    shadowColor: 'rgba(139, 159, 255, 0.25)',
    rightDecoration: ['#FF9EC7', '#7DD3FC'],
    leftDecoration: ['#67E8F9', '#8B9FFF']
  }
};

// 修改输出尺寸
const sizes = [16, 32, 48, 64, 128, 256, 512];

// 修改小精灵在画布中的尺寸和位置
// size * 0.83 = 小精灵尺寸（83%）
// size * 0.085 = 边距（8.5%）
drawGhostLogo(ctx, size * 0.83, size * 0.085, size * 0.085, colorModes[mode]);
```
