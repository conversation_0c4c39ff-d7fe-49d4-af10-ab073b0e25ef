require('dotenv').config();

console.log('🔍 验证项目配置...\n');

// 检查必需的环境变量
const requiredEnvs = {
  // 数据库配置
  'DB_HOST': process.env.DB_HOST,
  'DB_PORT': process.env.DB_PORT,
  'DB_USER': process.env.DB_USER,
  'DB_PASSWORD': process.env.DB_PASSWORD,
  'DB_NAME': process.env.DB_NAME,
  
  // JWT配置
  'JWT_SECRET': process.env.JWT_SECRET,
  
  // 阿里云短信配置
  'ALIYUN_ACCESS_KEY_ID': process.env.ALIYUN_ACCESS_KEY_ID,
  'ALIYUN_ACCESS_KEY_SECRET': process.env.ALIYUN_ACCESS_KEY_SECRET,
  'ALIYUN_SMS_SIGN_NAME': process.env.ALIYUN_SMS_SIGN_NAME,
  'ALIYUN_SMS_TEMPLATE_CODE': process.env.ALIYUN_SMS_TEMPLATE_CODE,
};

let configValid = true;

console.log('📋 环境变量检查:');
console.log('─'.repeat(50));

Object.entries(requiredEnvs).forEach(([key, value]) => {
  const isSecret = key.includes('SECRET') || key.includes('PASSWORD');
  const displayValue = isSecret ? (value ? '***已配置***' : '未配置') : (value || '未配置');
  
  if (!value || value === 'your_access_key_id' || value === 'your_access_key_secret') {
    console.log(`❌ ${key.padEnd(25)}: ${displayValue}`);
    configValid = false;
  } else {
    console.log(`✅ ${key.padEnd(25)}: ${displayValue}`);
  }
});

console.log('─'.repeat(50));

if (!configValid) {
  console.log('\n❌ 配置不完整！');
  console.log('\n📖 请参考以下文档完成配置:');
  console.log('   • docs/sms-setup.md - 阿里云短信服务配置');
  console.log('   • DEPLOYMENT-GUIDE.md - 完整部署指南');
  console.log('   • .env.example - 环境变量模板');
  process.exit(1);
} else {
  console.log('\n✅ 所有必需的环境变量都已配置！');
}

// 检查数据库连接
console.log('\n🗄️  测试数据库连接...');
const mysql = require('mysql2/promise');

async function testDatabaseConnection() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    });
    
    await connection.execute('SELECT 1');
    await connection.end();
    
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.log(`❌ 数据库连接失败: ${error.message}`);
    return false;
  }
}

// 检查必需的表是否存在
async function checkDatabaseTables() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    });
    
    const requiredTables = ['users', 'phone_verifications'];
    
    for (const table of requiredTables) {
      const [rows] = await connection.execute(
        'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
        [process.env.DB_NAME, table]
      );
      
      if (rows[0].count > 0) {
        console.log(`✅ 表 ${table} 存在`);
      } else {
        console.log(`❌ 表 ${table} 不存在`);
        console.log('   请运行: npm run db:init');
        await connection.end();
        return false;
      }
    }
    
    await connection.end();
    return true;
  } catch (error) {
    console.log(`❌ 检查数据库表失败: ${error.message}`);
    return false;
  }
}

async function runChecks() {
  const dbConnected = await testDatabaseConnection();
  
  if (dbConnected) {
    console.log('\n📊 检查数据库表结构...');
    const tablesExist = await checkDatabaseTables();
    
    if (tablesExist) {
      console.log('\n🎉 所有检查都通过了！');
      console.log('\n🚀 接下来可以:');
      console.log('   1. 运行 npm run test:sms 测试短信服务');
      console.log('   2. 运行 npm run dev 启动项目');
      console.log('   3. 访问 http://localhost:3141 测试登录功能');
    }
  } else {
    console.log('\n💡 数据库连接失败，请检查:');
    console.log('   • 数据库服务是否启动');
    console.log('   • 数据库配置是否正确');
    console.log('   • 网络连接是否正常');
  }
}

runChecks().catch(console.error);
