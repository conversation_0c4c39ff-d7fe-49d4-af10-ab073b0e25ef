"use client";
import { Plus, Minus } from "lucide-react";

interface NumberAdjusterProps {
  value: string;
  onChange: (value: string) => void;
  unit?: string;
  presets?: string[];
  step?: number;
  min?: number;
}

export function NumberAdjuster({ value, onChange, unit, presets, step = 1, min = 0 }: NumberAdjusterProps) {
  // 解析当前值，处理带单位的情况
  const parseValue = (val: string) => {
    if (!val) return 0;
    const numStr = val.replace(/[^\d.-]/g, '');
    return parseFloat(numStr) || 0;
  };
  
  const numericValue = parseValue(value);
  
  const increment = () => {
    const newValue = numericValue + step;
    onChange(`${newValue}${unit || ''}`);
  };
  
  const decrement = () => {
    const newValue = Math.max(min, numericValue - step);
    onChange(`${newValue}${unit || ''}`);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    onChange(`${inputValue}${unit || ''}`);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <div className="flex items-center bg-background rounded border border-border shadow-sm">
          <button
            onClick={decrement}
            className="p-1 hover:bg-muted transition-colors rounded-l"
          >
            <Minus className="w-3 h-3 text-muted-foreground" />
          </button>
          <input
            type="number"
            value={numericValue}
            onChange={handleInputChange}
            className="w-12 h-7 text-xs text-center bg-transparent border-none text-foreground focus:outline-none"
          />
          <button
            onClick={increment}
            className="p-1 hover:bg-muted transition-colors rounded-r"
          >
            <Plus className="w-3 h-3 text-muted-foreground" />
          </button>
        </div>
        {unit && (
          <span className="text-xs text-muted-foreground font-mono">
            {unit}
          </span>
        )}
      </div>
      
      {/* 快速预设值 - 更紧凑 */}
      {presets && (
        <div className="flex flex-wrap gap-1">
          {presets.slice(0, 5).map(preset => (
            <button
              key={preset}
              onClick={() => onChange(preset)}
              className={`px-1.5 py-0.5 text-xs rounded transition-all duration-200 ${
                value === preset
                  ? 'bg-primary text-primary-foreground shadow-sm'
                  : 'bg-muted/50 text-muted-foreground hover:bg-primary/10 hover:text-primary border border-border/50'
              }`}
            >
              {preset}
            </button>
          ))}
        </div>
      )}
    </div>
  );
} 