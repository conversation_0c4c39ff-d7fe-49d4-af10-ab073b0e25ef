"use client";

import { useState, useEffect } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Coins, 
  TrendingUp, 
  TrendingDown, 
  History, 
  ArrowLeft,
  RefreshCw
} from "lucide-react";

export default function PointsSimplePage() {
  const { user, loading } = useUser();
  const router = useRouter();

  // 如果用户未登录，重定向到首页
  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 简化的顶部栏 */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <Button
            variant="ghost"
            onClick={() => router.push('/')}
            className="flex items-center gap-2 font-semibold"
          >
            <Coins className="w-5 h-5" />
            LoomRun
          </Button>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* 页面标题 */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Coins className="w-8 h-8 text-blue-600" />
              我的积分
            </h1>
            <p className="text-muted-foreground mt-1">管理和查看您的积分详情</p>
          </div>
        </div>

        {/* 积分概览卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 border-blue-200 dark:border-blue-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">当前积分</p>
                  <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">{user.points?.toLocaleString() || 0}</p>
                </div>
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Coins className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50 border-green-200 dark:border-green-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600 dark:text-green-400">累计获得</p>
                  <p className="text-3xl font-bold text-green-700 dark:text-green-300">{user.total_earned_points?.toLocaleString() || 0}</p>
                </div>
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/50 dark:to-red-900/50 border-red-200 dark:border-red-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-600 dark:text-red-400">累计消费</p>
                  <p className="text-3xl font-bold text-red-700 dark:text-red-300">{user.total_spent_points?.toLocaleString() || 0}</p>
                </div>
                <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                  <TrendingDown className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 功能说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="w-5 h-5" />
              积分系统说明
            </CardTitle>
            <CardDescription>了解积分的获得方式和使用规则</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-green-600">积分获得方式</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                      注册奖励
                    </Badge>
                    <span className="text-sm">新用户注册获得100积分</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                      邀请奖励
                    </Badge>
                    <span className="text-sm">邀请好友注册获得100积分</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
                      充值获得
                    </Badge>
                    <span className="text-sm">充值购买积分，永久有效</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-semibold text-red-600">积分消费方式</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400">
                      AI对话
                    </Badge>
                    <span className="text-sm">使用AI模型进行对话</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400">
                      项目导出
                    </Badge>
                    <span className="text-sm">导出项目到各种格式</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
                      高级功能
                    </Badge>
                    <span className="text-sm">使用系统的高级功能</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <h5 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">积分有效期说明</h5>
              <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• 活动积分（注册、邀请）：有效期30天</li>
                <li>• 订阅积分：按订阅周期有效</li>
                <li>• 充值积分：永久有效</li>
                <li>• 消费优先级：订阅积分 → 活动积分 → 充值积分</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* 操作按钮 */}
        <div className="mt-8 flex gap-4">
          <Button 
            onClick={() => router.push('/points')}
            className="flex items-center gap-2"
          >
            <History className="w-4 h-4" />
            查看详细历史
          </Button>
          
          <Button 
            variant="outline"
            onClick={() => router.push('/test-points')}
            className="flex items-center gap-2"
          >
            <Coins className="w-4 h-4" />
            积分功能测试
          </Button>
        </div>
      </div>
    </div>
  );
}
