"use client";

import React, { useEffect, useState } from 'react';
import { useThemeLanguage } from '@/components/providers/theme-language-provider';

interface GhostLogoProps {
  className?: string;
  size?: number;
  isWorking?: boolean; // 新增：是否处于工作状态
}

export function GhostLogo({ className = '', size = 40, isWorking = false }: GhostLogoProps) {
  const [isClient, setIsClient] = useState(false);
  const { resolvedTheme } = useThemeLanguage();

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return null; // 避免SSR问题
  }

  // 确定当前是否为浅色模式
  const isLightMode = resolvedTheme === 'light';

  // 根据主题选择颜色方案
  const colors = isLightMode ? {
    // 浅色模式颜色
    mainGradient: 'linear-gradient(135deg, #8B9FFF, #A78BFA, #F8BBD9)',
    shadowColor: 'rgba(139, 159, 255, 0.25)',
    rightDecoration: 'linear-gradient(135deg, #FF9EC7, #7DD3FC)',
    leftDecoration: 'linear-gradient(135deg, #67E8F9, #8B9FFF)'
  } : {
    // 深色模式颜色
    mainGradient: 'linear-gradient(135deg, #FFB3BA, #BAFFC9, #BAE1FF)',
    shadowColor: 'rgba(255, 179, 186, 0.4)',
    rightDecoration: 'linear-gradient(135deg, #FF8A95, #85E3FF)',
    leftDecoration: 'linear-gradient(135deg, #85E3FF, #A8E6CF)'
  };

  return (
    <>
      <style jsx global>{`
        @keyframes ghostBlink {
          0%, 92%, 100% {
            transform: scaleY(1);
          }
          96% {
            transform: scaleY(0.1);
          }
        }

        @keyframes ghostBlinkHover {
          0%, 85%, 100% {
            transform: scaleY(1);
          }
          90% {
            transform: scaleY(0.1);
          }
        }

        @keyframes ghostBounce {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-4px);
          }
        }

        .ghost-container:hover .ghost-eye {
          animation: ghostBlinkHover 2s infinite !important;
        }

        .ghost-working {
          animation: ghostBounce 1.2s ease-in-out infinite;
        }
      `}</style>

      <div
        className={`ghost-container ${className} cursor-pointer ${isWorking ? 'ghost-working' : ''}`}
        style={{
          width: `${size}px`,
          height: `${size * 1.1}px`,
          position: 'relative'
        }}
      >
        <div
          className="ghost-body"
          style={{
            width: `${size * 0.9}px`,
            height: `${size}px`,
            background: colors.mainGradient,
            borderRadius: `${size * 0.45}px ${size * 0.45}px ${size * 0.35}px ${size * 0.3}px`,
            position: 'relative',
            margin: `${size * 0.05}px auto 0`,
            boxShadow: `0 ${size * 0.08}px ${size * 0.2}px ${colors.shadowColor}`,
            transform: 'rotate(-2deg)'
          }}
        >
          {/* 右侧不规则形状 */}
          <div
            style={{
              position: 'absolute',
              top: `${size * 0.12}px`,
              right: `${-size * 0.06}px`,
              width: `${size * 0.2}px`,
              height: `${size * 0.25}px`,
              background: colors.rightDecoration,
              borderRadius: '50% 30% 60% 40%',
              transform: 'rotate(15deg)'
            }}
          />

          {/* 左侧不规则形状 */}
          <div
            style={{
              position: 'absolute',
              bottom: `${size * 0.12}px`,
              left: `${-size * 0.06}px`,
              width: `${size * 0.2}px`,
              height: `${size * 0.25}px`,
              background: colors.leftDecoration,
              borderRadius: '50% 30% 60% 40%',
              transform: 'rotate(-165deg)'
            }}
          />

          {/* 眼睛 */}
          <div
            style={{
              position: 'absolute',
              top: `${size * 0.3}px`,
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: `${size * 0.1}px`,
              zIndex: 2
            }}
          >
            <div
              className="ghost-eye"
              style={{
                width: `${size * 0.11}px`,
                height: `${size * 0.16}px`,
                background: '#1F2937',
                borderRadius: '50%',
                animation: 'ghostBlink 4s infinite',
                boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
              }}
            />
            <div
              className="ghost-eye"
              style={{
                width: `${size * 0.11}px`,
                height: `${size * 0.16}px`,
                background: '#1F2937',
                borderRadius: '50%',
                animation: 'ghostBlink 4s infinite',
                boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
}
