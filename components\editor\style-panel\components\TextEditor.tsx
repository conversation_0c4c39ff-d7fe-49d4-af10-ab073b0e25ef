"use client";
import { useCallback, useState, useEffect, useRef } from "react";

interface TextEditorProps {
  selectedElement: HTMLElement | null;
  onPreviewStyles: (styles: CSSStyleDeclaration) => void;
}

export function TextEditor({ selectedElement, onPreviewStyles }: TextEditorProps) {
  const [textContent, setTextContent] = useState('');
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const isUpdatingRef = useRef(false);

  // 检查元素是否可编辑文本
  const isTextEditable = useCallback((element: HTMLElement) => {
    const tagName = element.tagName.toLowerCase();
    const editableTags = ['button', 'a', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'label', 'div'];
    return editableTags.includes(tagName);
  }, []);

  // 初始化文本内容
  useEffect(() => {
    if (selectedElement && !isUpdatingRef.current) {
      const content = selectedElement.textContent || '';
      setTextContent(content);
    }
  }, [selectedElement]);

  // 防抖处理文本内容变化
  const handleTextContentChange = useCallback((newContent: string) => {
    setTextContent(newContent);
    
    // 清除之前的防抖定时器
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    // 设置新的防抖定时器
    debounceTimeoutRef.current = setTimeout(() => {
      if (selectedElement) {
        isUpdatingRef.current = true;
        selectedElement.textContent = newContent;
        // 只在必要时触发预览更新
        onPreviewStyles(selectedElement.style);
        isUpdatingRef.current = false;
      }
    }, 150); // 150ms 防抖延迟
  }, [selectedElement, onPreviewStyles]);

  // 立即应用文本变化（用于预设按钮）
  const applyTextContentImmediately = useCallback((newContent: string) => {
    setTextContent(newContent);
    if (selectedElement) {
      isUpdatingRef.current = true;
      selectedElement.textContent = newContent;
      onPreviewStyles(selectedElement.style);
      isUpdatingRef.current = false;
    }
  }, [selectedElement, onPreviewStyles]);

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // 获取文本预设
  const getTextPresets = useCallback((element: HTMLElement) => {
    const tagName = element.tagName.toLowerCase();
    
    if (tagName === 'button') {
      return ['确定', '取消', '提交', '保存', '删除', '编辑', '查看详情', '立即购买'];
    } else if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
      return ['主标题', '副标题', '章节标题', '产品介绍', '服务特色', '联系我们'];
    } else if (tagName === 'a') {
      return ['了解更多', '立即体验', '联系我们', '返回首页', '查看详情', '下载'];
    } else if (['p', 'span', 'div'].includes(tagName)) {
      return ['这是一段示例文本', '欢迎使用我们的产品', '感谢您的支持', '请联系客服'];
    } else {
      return ['示例文本', '标题', '描述', '提示信息'];
    }
  }, []);

  if (!selectedElement || !isTextEditable(selectedElement)) {
    return (
      <div className="space-y-3">
        <div className="text-sm text-muted-foreground text-center py-6">
          当前选中的元素不支持文本编辑
        </div>
        <div className="text-xs text-muted-foreground text-center">
          请选择按钮、链接、标题或文本元素
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 文本内容编辑 */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          文本内容
        </label>
        <textarea
          value={textContent}
          onChange={(e) => handleTextContentChange(e.target.value)}
          placeholder="输入文本内容..."
          className="w-full h-20 p-3 text-sm bg-background border border-border rounded-md text-foreground placeholder:text-muted-foreground focus:border-primary focus:outline-none resize-none shadow-sm"
        />
        <div className="text-xs text-muted-foreground">
          当前元素: {selectedElement.tagName.toLowerCase()}
        </div>
      </div>

      {/* 常用文本预设 */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          常用文本
        </label>
        <div className="grid gap-2">
          {getTextPresets(selectedElement).map((preset, index) => (
            <button
              key={index}
              onClick={() => applyTextContentImmediately(preset)}
              className="w-full p-2 text-sm text-left bg-muted hover:bg-muted/80 border border-border hover:border-primary/50 rounded-md transition-all duration-200 text-foreground hover:text-primary"
            >
              {preset}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
} 