import { TailwindCleaner } from './TailwindCleaner';

// 样式管理工具类 - 保持HTML结构清洁，防止Tailwind污染
export class StyleManager {
  private static customStylesMap = new Map<string, Record<string, string>>();
  private static styleCounter = 0;

  // 生成唯一的样式类名
  static generateStyleClass(elementId?: string): string {
    this.styleCounter++;
    return elementId ? `custom-${elementId}-${this.styleCounter}` : `custom-style-${this.styleCounter}`;
  }

  // 提取元素的自定义样式
  static extractCustomStyles(element: HTMLElement): Record<string, string> {
    const customStyles: Record<string, string> = {};
    const inlineStyle = element.style;
    
    // 常用的可提取样式属性
    const extractableProps = [
      'color', 'background-color', 'font-size', 'font-weight', 
      'text-align', 'padding', 'margin', 'border-radius', 
      'border', 'opacity', 'transform', 'box-shadow'
    ];

    extractableProps.forEach(prop => {
      const value = inlineStyle.getPropertyValue(prop);
      if (value && value.trim()) {
        customStyles[prop] = value;
      }
    });

    return customStyles;
  }

  // 清理元素的内联样式（公共方法）
  static cleanInlineStyles(element: HTMLElement, propsToClean: string[]): void {
    propsToClean.forEach(prop => {
      element.style.removeProperty(prop);
    });
    console.log(`✅ StyleManager: 已清理内联样式`, { 
      element: element.tagName, 
      cleanedProps: propsToClean 
    });
  }

  // 生成CSS规则字符串
  static generateCSSRule(className: string, styles: Record<string, string>): string {
    const cssProps = Object.entries(styles)
      .map(([prop, value]) => `  ${prop}: ${value};`)
      .join('\n');
    
    return `.${className} {\n${cssProps}\n}`;
  }

  // 获取或创建style标签（支持iframe）- 修复版本
  static getOrCreateStyleTag(): HTMLStyleElement {
    // 优先在iframe中查找和创建style标签
    const iframe = document.querySelector('iframe');
    const targetDoc = (iframe && iframe.contentDocument) ? iframe.contentDocument : document;

    // 🔧 修复：验证文档完整性
    if (!targetDoc || !targetDoc.documentElement) {
      console.warn('⚠️ 目标文档不完整，使用主文档');
      return this.createStyleTagInDocument(document);
    }

    let styleTag = targetDoc.getElementById('loomrun-custom-styles') as HTMLStyleElement;

    if (!styleTag) {
      return this.createStyleTagInDocument(targetDoc);
    }

    return styleTag;
  }

  // 🔧 新增：在指定文档中安全地创建样式标签
  private static createStyleTagInDocument(targetDoc: Document): HTMLStyleElement {
    try {
      const styleTag = targetDoc.createElement('style');
      styleTag.id = 'loomrun-custom-styles';
      styleTag.type = 'text/css';

      // 🔧 安全地确保head存在
      if (!targetDoc.head) {
        const head = targetDoc.createElement('head');

        // 🔧 安全地插入head
        if (targetDoc.documentElement) {
          targetDoc.documentElement.insertBefore(head, targetDoc.body);
        } else {
          // 如果documentElement不存在，创建基本HTML结构
          console.warn('⚠️ 文档结构不完整，创建基本HTML结构');
          const html = targetDoc.createElement('html');
          const body = targetDoc.createElement('body');
          html.appendChild(head);
          html.appendChild(body);
          targetDoc.appendChild(html);
        }
      }

      targetDoc.head.appendChild(styleTag);
      console.log('✅ 在目标文档中创建了样式标签', {
        isIframe: targetDoc !== document,
        documentTitle: targetDoc.title || 'untitled',
        hasDocumentElement: !!targetDoc.documentElement,
        hasHead: !!targetDoc.head
      });

      return styleTag;
    } catch (error) {
      console.error('❌ 创建样式标签失败，回退到主文档:', error);
      // 回退到主文档
      if (targetDoc !== document) {
        return this.createStyleTagInDocument(document);
      }
      throw error;
    }
  }

  // 应用样式到CSS而不是内联
  static applyStylesToCSS(element: HTMLElement, styles: Record<string, string>): string {
    // 检查元素是否已有自定义类
    let customClass = Array.from(element.classList).find(cls => cls.startsWith('custom-'));
    
    // 如果没有自定义类，创建一个
    if (!customClass) {
      customClass = this.generateStyleClass(element.id);
      element.classList.add(customClass);
    }

    // 存储样式映射
    this.customStylesMap.set(customClass, styles);

    // 获取style标签
    const styleTag = this.getOrCreateStyleTag();
    
    // 生成CSS规则
    const cssRule = this.generateCSSRule(customClass, styles);
    
    // 更新style标签内容
    this.updateStyleTagContent(styleTag, customClass, cssRule);

    // 🔧 修复：立即清理内联样式，避免重复
    // 使用requestAnimationFrame确保DOM更新后执行
    requestAnimationFrame(() => {
      this.cleanInlineStyles(element, Object.keys(styles));
      console.log('🧹 内联样式已清理，只保留CSS类', {
        element: element.tagName,
        customClass,
        remainingInlineStyles: element.style.cssText
      });
    });
    
    console.log(`✅ StyleManager: 样式已应用到CSS类 ${customClass}`, {
      styles,
      cssRule: cssRule.substring(0, 100) + '...'
    });
    
    return customClass;
  }

  // 更新style标签内容
  private static updateStyleTagContent(styleTag: HTMLStyleElement, className: string, newRule: string): void {
    let content = styleTag.textContent || '';
    
    // 移除旧的规则（如果存在）
    const oldRuleRegex = new RegExp(`\\.${className}\\s*\\{[^}]*\\}`, 'g');
    content = content.replace(oldRuleRegex, '');
    
    // 添加新规则
    content += '\n' + newRule;
    
    // 清理多余的空行
    content = content.replace(/\n\s*\n/g, '\n').trim();
    
    styleTag.textContent = content;
  }

  // 重置元素样式
  static resetElementStyles(element: HTMLElement): void {
    // 找到自定义类
    const customClass = Array.from(element.classList).find(cls => cls.startsWith('custom-'));
    
    if (customClass) {
      // 从元素移除自定义类
      element.classList.remove(customClass);
      
      // 从映射中移除
      this.customStylesMap.delete(customClass);
      
      // 从CSS中移除规则
      const styleTag = this.getOrCreateStyleTag();
      let content = styleTag.textContent || '';
      const ruleRegex = new RegExp(`\\.${customClass}\\s*\\{[^}]*\\}`, 'g');
      content = content.replace(ruleRegex, '');
      styleTag.textContent = content.trim();
    }
    
    // 清理任何剩余的内联样式
    const commonProps = [
      'color', 'background-color', 'font-size', 'font-weight', 
      'text-align', 'padding', 'margin', 'border-radius', 
      'border', 'opacity'
    ];
    
    this.cleanInlineStyles(element, commonProps);
  }

  // 获取元素的有效样式（包括CSS类和内联样式）
  static getEffectiveStyles(element: HTMLElement): Record<string, string> {
    const computedStyles = window.getComputedStyle(element);
    const inlineStyles = element.style;
    const customClass = Array.from(element.classList).find(cls => cls.startsWith('custom-'));
    
    const effectiveStyles: Record<string, string> = {};
    const commonProps = [
      'color', 'background-color', 'font-size', 'font-weight', 
      'text-align', 'padding', 'margin', 'border-radius', 
      'border', 'opacity'
    ];

    commonProps.forEach(prop => {
      // 优先级：内联样式 > 自定义CSS类 > 计算样式
      let value = inlineStyles.getPropertyValue(prop);
      
      if (!value && customClass) {
        const customStyles = this.customStylesMap.get(customClass);
        if (customStyles && customStyles[prop]) {
          value = customStyles[prop];
        }
      }
      
      if (!value) {
        value = computedStyles.getPropertyValue(prop);
      }
      
      if (value && value.trim() && !this.isDefaultValue(prop, value)) {
        effectiveStyles[prop] = value;
      }
    });

    return effectiveStyles;
  }

  // 检查是否为默认值
  private static isDefaultValue(prop: string, value: string): boolean {
    const defaultValues = {
      'opacity': '1',
      'font-weight': '400',
      'color': 'rgba(0, 0, 0, 0)',
      'background-color': 'rgba(0, 0, 0, 0)',
      'padding': '0px',
      'margin': '0px',
      'border': 'none',
      'border-radius': '0px'
    };

    return value === 'auto' || 
           value === 'initial' || 
           value === 'normal' || 
           value === 'none' ||
           value === 'transparent' ||
           value === defaultValues[prop as keyof typeof defaultValues];
  }

  // 导出当前的自定义样式为CSS字符串
  static exportCustomCSS(): string {
    const styleTag = this.getOrCreateStyleTag();
    return styleTag.textContent || '';
  }

  // 清理所有自定义样式
  static clearAllCustomStyles(): void {
    this.customStylesMap.clear();
    const styleTag = document.getElementById('loomrun-custom-styles');
    if (styleTag) {
      styleTag.textContent = '';
    }
  }

  // 清理HTML中的Tailwind重复内容 - 新增方法
  static cleanTailwindDuplicates(html: string): string {
    console.log('🧹 StyleManager: 开始清理Tailwind重复内容...');
    return TailwindCleaner.cleanHTML(html);
  }

  // 实时清理Tailwind重复内容 - 用于样式编辑过程中
  static realtimeCleanTailwind(html: string): string {
    return TailwindCleaner.realtimeClean(html);
  }

  // 确保HTML包含正确的Tailwind支持
  static ensureTailwindSupport(html: string): string {
    return TailwindCleaner.ensureSingleTailwindCDN(html);
  }
} 