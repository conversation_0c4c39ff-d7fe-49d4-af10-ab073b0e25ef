import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

// 简化的 className 合并函数
const cn = (...classes: (string | undefined | boolean)[]) => {
  return classes.filter(Boolean).join(' ');
};

interface ModelOption {
  id: string;
  name: string;
  description: string;
  signal: number; // 1-4 信号强度
}

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  disabled?: boolean;
  className?: string;
  isWelcomeMode?: boolean;
}

const MODELS: ModelOption[] = [
  {
    id: 'loomrun-1.2ds',
    name: 'loomrun-1.2ds',
    description: '',
    signal: 3
  },
  {
    id: 'loomrun-1.6db',
    name: 'loomrun-1.6db', 
    description: '',
    signal: 4
  }
];

// 信号图标组件 - 紧凑版本，修复水合错误
const SignalIcon: React.FC<{ strength: number; className?: string }> = ({ strength, className }) => {
  // 使用固定的CSS类而不是条件渲染，避免水合错误
  const bars = [
    { height: "h-0.5", active: strength >= 1 },
    { height: "h-1", active: strength >= 2 },
    { height: "h-1.5", active: strength >= 3 },
    { height: "h-2", active: strength >= 4 }
  ];

  return (
    <div className={cn("flex items-center gap-px", className)}>
      {bars.map((bar, index) => (
        <div
          key={index}
          className={cn(
            "w-px rounded-sm transition-colors",
            bar.height,
            bar.active ? "bg-green-500" : "bg-gray-400 dark:bg-gray-600"
          )}
        />
      ))}
    </div>
  );
};

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  disabled = false,
  className,
  isWelcomeMode = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedOption = MODELS.find(model => model.id === selectedModel) || MODELS[0];

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId);
    setIsOpen(false);
  };

  return (
    <div className={cn("relative", className)} ref={dropdownRef} style={{zIndex: isOpen ? 9999 : 'auto'}}>
      {/* 触发按钮 - 响应式紧凑设计 */}
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!disabled) {
            setIsOpen(!isOpen);
          }
        }}
        disabled={disabled}
        className={cn(
          "flex items-center gap-0.5 px-0.5 py-0.5 rounded transition-all duration-200",
          "bg-transparent border-none min-w-0 max-w-full",
          isWelcomeMode 
            ? "hover:bg-white/10 dark:hover:bg-white/10 hover:bg-gray-100/50 text-gray-700 dark:text-neutral-300 hover:text-blue-600 dark:hover:text-blue-300"
            : "hover:bg-white/10 text-neutral-400 hover:text-blue-400",
          "focus:outline-none",
          disabled && "opacity-50 cursor-not-allowed"
        )}
      >
        {/* 信号图标 - 紧凑设计 */}
        <SignalIcon strength={selectedOption.signal} className="w-2.5 h-2.5 flex-shrink-0" />
        
        {/* 模型名称 - 始终显示，紧凑设计 */}
        <span className="text-xs font-medium truncate">
          {selectedOption.name}
        </span>
        
        {/* 下拉箭头 - 始终显示，确保用户知道可以点击 */}
        <ChevronDown 
          className={cn(
            "w-2 h-2 transition-transform duration-200 flex-shrink-0",
            isOpen && "rotate-180"
          )}
        />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute bottom-full left-0 mb-2 w-36 bg-neutral-900 dark:bg-neutral-900 bg-white rounded-md shadow-lg border border-neutral-700 dark:border-neutral-700 border-gray-300 z-[9999]">
          <div className="py-1">
            {MODELS.map((model) => (
              <button
                key={model.id}
                onClick={() => handleModelSelect(model.id)}
                className={cn(
                  "w-full flex items-center gap-2 px-3 py-1.5 transition-colors text-left text-sm",
                  "hover:bg-neutral-800 dark:hover:bg-neutral-800 hover:bg-gray-100 text-neutral-300 dark:text-neutral-300 text-gray-700 hover:text-white dark:hover:text-white hover:text-gray-900",
                  selectedModel === model.id && "bg-neutral-800 dark:bg-neutral-800 bg-blue-50 text-white dark:text-white text-blue-700"
                )}
              >
                {/* 信号图标 */}
                <SignalIcon strength={model.signal} className="w-3 h-3 flex-shrink-0" />
                
                {/* 模型信息 */}
                <div className="flex-1 min-w-0">
                  <span className="font-medium block truncate">
                    {model.name}
                  </span>
                  {model.description && (
                    <span className="text-xs text-neutral-500 block">
                      {model.description}
                    </span>
                  )}
                </div>
                
                {/* 选中指示器 */}
                {selectedModel === model.id && (
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full flex-shrink-0" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}; 