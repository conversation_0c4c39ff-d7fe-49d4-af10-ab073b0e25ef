"use client";

import { useState } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Coins, ExternalLink, User, RefreshCw } from "lucide-react";
import Link from "next/link";

export default function TestPointsPagePage() {
  const { user, refreshUser } = useUser();
  const [refreshing, setRefreshing] = useState(false);

  const handleRefreshUser = async () => {
    setRefreshing(true);
    try {
      await refreshUser();
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2">
        <Coins className="w-6 h-6" />
        <h1 className="text-2xl font-bold">积分详情页面测试</h1>
      </div>

      {/* 用户状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            用户状态
          </CardTitle>
          <CardDescription>当前登录状态和积分信息</CardDescription>
        </CardHeader>
        <CardContent>
          {user ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{user.points || 0}</div>
                  <div className="text-sm text-muted-foreground">当前积分</div>
                </div>
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{user.total_earned_points || 0}</div>
                  <div className="text-sm text-muted-foreground">累计获得</div>
                </div>
                <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{user.total_spent_points || 0}</div>
                  <div className="text-sm text-muted-foreground">累计消费</div>
                </div>
                <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="text-lg font-bold text-purple-600">{user.nickname || user.name}</div>
                  <div className="text-sm text-muted-foreground">用户昵称</div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={handleRefreshUser} 
                  disabled={refreshing}
                  variant="outline"
                  size="sm"
                >
                  {refreshing && <RefreshCw className="w-4 h-4 mr-2 animate-spin" />}
                  刷新用户信息
                </Button>
                
                <Link href="/points">
                  <Button className="flex items-center gap-2">
                    <Coins className="w-4 h-4" />
                    查看积分详情
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">请先登录以查看积分信息</p>
              <Link href="/">
                <Button>前往首页登录</Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 功能说明 */}
      <Card>
        <CardHeader>
          <CardTitle>积分详情页面功能</CardTitle>
          <CardDescription>积分详情页面包含的主要功能</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold">积分概览</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 当前积分余额</li>
                <li>• 累计获得积分</li>
                <li>• 累计消费积分</li>
                <li>• 渐变色卡片设计</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-semibold">积分历史</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 所有积分变动记录</li>
                <li>• 积分来源和类型标识</li>
                <li>• 有效期显示和提醒</li>
                <li>• 图标和颜色区分</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-semibold">积分余额</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 按类型分组显示</li>
                <li>• 活动/订阅/充值积分</li>
                <li>• 有效期管理</li>
                <li>• 即将过期提醒</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-semibold">界面特性</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 深色/浅色模式支持</li>
                <li>• 响应式设计</li>
                <li>• 专业的视觉设计</li>
                <li>• 流畅的交互体验</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API 测试 */}
      <Card>
        <CardHeader>
          <CardTitle>API 接口测试</CardTitle>
          <CardDescription>测试积分相关的API接口</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              variant="outline" 
              onClick={() => window.open('/api/points/history', '_blank')}
              className="flex items-center gap-2"
            >
              <ExternalLink className="w-4 h-4" />
              积分历史 API
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.open('/api/points/balance', '_blank')}
              className="flex items-center gap-2"
            >
              <ExternalLink className="w-4 h-4" />
              积分余额 API
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.open('/api/points/stats', '_blank')}
              className="flex items-center gap-2"
            >
              <ExternalLink className="w-4 h-4" />
              积分统计 API
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
          <CardDescription>如何访问积分详情页面</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
              <span>点击顶部用户菜单中的积分卡片"详情"按钮</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
              <span>直接访问 <code className="bg-muted px-1 rounded">/points</code> 路径</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></div>
              <span>页面会自动检查登录状态，未登录用户会被重定向到首页</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2"></div>
              <span>页面保留系统顶部栏，支持深色和浅色模式</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
