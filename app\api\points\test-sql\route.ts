import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery, initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);
    
    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    const tests = [];

    // 1. 测试简单的积分交易查询
    try {
      const simpleQuery = `
        SELECT COUNT(*) as total 
        FROM points_transactions pt 
        WHERE pt.user_id = ?
      `;
      const simpleResult = await executeQuery(simpleQuery, [user.id]) as { total: number }[];
      
      tests.push({
        name: '简单查询测试',
        status: 'success',
        query: simpleQuery,
        result: simpleResult[0]?.total || 0,
        message: '基础查询正常'
      });
    } catch (error) {
      tests.push({
        name: '简单查询测试',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '基础查询失败'
      });
    }

    // 2. 测试联表查询
    try {
      const joinQuery = `
        SELECT 
          pt.id,
          pt.user_id,
          pt.transaction_type,
          pt.points_amount,
          pt.source_type,
          pt.created_at,
          upb.expires_at as balance_expires_at
        FROM points_transactions pt
        LEFT JOIN user_points_balance upb ON pt.balance_record_id = upb.id
        WHERE pt.user_id = ?
        ORDER BY pt.created_at DESC
        LIMIT 5
      `;
      const joinResult = await executeQuery(joinQuery, [user.id]) as any[];
      
      tests.push({
        name: '联表查询测试',
        status: 'success',
        query: joinQuery,
        result: joinResult.length,
        data: joinResult,
        message: '联表查询正常'
      });
    } catch (error) {
      tests.push({
        name: '联表查询测试',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '联表查询失败'
      });
    }

    // 3. 测试带筛选条件的查询
    try {
      const filterQuery = `
        SELECT 
          pt.id,
          pt.transaction_type,
          pt.points_amount,
          pt.source_type,
          pt.points_type
        FROM points_transactions pt
        LEFT JOIN user_points_balance upb ON pt.balance_record_id = upb.id
        WHERE pt.user_id = ? AND pt.transaction_type = ?
        ORDER BY pt.created_at DESC
        LIMIT 3
      `;
      const filterResult = await executeQuery(filterQuery, [user.id, 'earn']) as any[];
      
      tests.push({
        name: '筛选查询测试',
        status: 'success',
        query: filterQuery,
        result: filterResult.length,
        data: filterResult,
        message: '筛选查询正常'
      });
    } catch (error) {
      tests.push({
        name: '筛选查询测试',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '筛选查询失败'
      });
    }

    // 4. 测试完整的历史记录查询（模拟真实API）
    try {
      const fullQuery = `
        SELECT 
          pt.id,
          pt.user_id,
          pt.transaction_type,
          pt.points_amount,
          pt.balance_before,
          pt.balance_after,
          pt.source_type,
          pt.points_type,
          pt.expires_at,
          pt.balance_record_id,
          pt.source_id,
          pt.description,
          pt.metadata,
          pt.created_at,
          upb.expires_at as balance_expires_at,
          upb.is_active as balance_is_active
        FROM points_transactions pt
        LEFT JOIN user_points_balance upb ON pt.balance_record_id = upb.id
        WHERE pt.user_id = ?
        ORDER BY pt.created_at DESC, pt.id DESC
        LIMIT 10 OFFSET 0
      `;
      const fullResult = await executeQuery(fullQuery, [user.id]) as any[];
      
      tests.push({
        name: '完整历史查询测试',
        status: 'success',
        query: fullQuery,
        result: fullResult.length,
        data: fullResult.slice(0, 2), // 只返回前2条作为示例
        message: '完整查询正常'
      });
    } catch (error) {
      tests.push({
        name: '完整历史查询测试',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '完整查询失败'
      });
    }

    // 5. 测试表结构信息
    try {
      const structureQuery = `
        DESCRIBE points_transactions
      `;
      const structureResult = await executeQuery(structureQuery, []) as any[];
      
      tests.push({
        name: '表结构查询',
        status: 'success',
        result: structureResult.length,
        data: structureResult,
        message: '表结构查询正常'
      });
    } catch (error) {
      tests.push({
        name: '表结构查询',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: '表结构查询失败'
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        userId: user.id,
        testResults: tests,
        summary: {
          totalTests: tests.length,
          successCount: tests.filter(t => t.status === 'success').length,
          errorCount: tests.filter(t => t.status === 'error').length
        }
      }
    });

  } catch (error) {
    console.error("SQL测试失败:", error);
    return NextResponse.json({ 
      success: false,
      error: "SQL测试失败",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
