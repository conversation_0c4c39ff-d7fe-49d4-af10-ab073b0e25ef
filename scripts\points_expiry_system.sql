DROP TABLE IF EXISTS user_points_balance;
CREATE TABLE user_points_balance (
  id int NOT NULL AUTO_INCREMENT,
  user_id int NOT NULL,
  points_type enum('activity','subscription','recharge') NOT NULL,
  points_amount int NOT NULL DEFAULT 0,
  expires_at datetime DEFAULT NULL,
  source_order_id int DEFAULT NULL,
  source_plan_key varchar(100) DEFAULT NULL,
  is_active tinyint(1) NOT NULL DEFAULT 1,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_user_id (user_id),
  KEY idx_points_type (points_type),
  KEY idx_expires_at (expires_at),
  KEY idx_user_type_active (user_id, points_type, is_active),
  KEY idx_user_expires (user_id, expires_at, is_active),
  CONSTRAINT user_points_balance_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

ALTER TABLE points_transactions ADD COLUMN points_type enum('activity','subscription','recharge') DEFAULT NULL AFTER source_type;
ALTER TABLE points_transactions ADD COLUMN expires_at datetime DEFAULT NULL AFTER points_type;
ALTER TABLE points_transactions ADD COLUMN balance_record_id int DEFAULT NULL AFTER expires_at;

ALTER TABLE points_transactions ADD INDEX idx_points_type (points_type);
ALTER TABLE points_transactions ADD INDEX idx_expires_at (expires_at);
ALTER TABLE points_transactions ADD INDEX idx_balance_record (balance_record_id);

ALTER TABLE subscription_plans ADD COLUMN plan_type enum('free','pro','max') NOT NULL DEFAULT 'free' AFTER plan_key;
ALTER TABLE subscription_plans ADD COLUMN points_validity_days int NOT NULL DEFAULT 30 AFTER points_included;

ALTER TABLE recharge_orders ADD COLUMN points_validity_days int NOT NULL DEFAULT 0 AFTER points_amount;

CREATE TABLE points_consumption_log (
  id int NOT NULL AUTO_INCREMENT,
  user_id int NOT NULL,
  transaction_id int NOT NULL,
  balance_record_id int NOT NULL,
  points_consumed int NOT NULL,
  points_type enum('activity','subscription','recharge') NOT NULL,
  consumed_at timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_user_id (user_id),
  KEY idx_transaction_id (transaction_id),
  KEY idx_balance_record_id (balance_record_id),
  KEY idx_points_type (points_type),
  KEY idx_consumed_at (consumed_at),
  CONSTRAINT points_consumption_log_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  CONSTRAINT points_consumption_log_ibfk_2 FOREIGN KEY (transaction_id) REFERENCES points_transactions (id) ON DELETE CASCADE,
  CONSTRAINT points_consumption_log_ibfk_3 FOREIGN KEY (balance_record_id) REFERENCES user_points_balance (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE points_expiry_log (
  id int NOT NULL AUTO_INCREMENT,
  user_id int NOT NULL,
  balance_record_id int NOT NULL,
  points_expired int NOT NULL,
  points_type enum('activity','subscription','recharge') NOT NULL,
  original_expires_at datetime NOT NULL,
  expired_at timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_user_id (user_id),
  KEY idx_balance_record_id (balance_record_id),
  KEY idx_points_type (points_type),
  KEY idx_expired_at (expired_at),
  CONSTRAINT points_expiry_log_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category) VALUES
('activity_points_validity_days', '30', 'number', '活动积分有效期天数', 'points'),
('free_plan_monthly_points', '50', 'number', '免费版每月积分数量', 'subscription'),
('free_plan_points_validity_days', '30', 'number', '免费版积分有效期天数', 'subscription'),
('points_consumption_priority', 'subscription,activity,recharge', 'string', '积分消费优先级顺序', 'points');

UPDATE subscription_plans SET plan_type = 'free', points_validity_days = 30 WHERE plan_key LIKE '%free%' OR plan_key LIKE '%basic%';
UPDATE subscription_plans SET plan_type = 'pro', points_validity_days = 30 WHERE plan_key LIKE '%pro%';
UPDATE subscription_plans SET plan_type = 'max', points_validity_days = 30 WHERE plan_key LIKE '%max%' OR plan_key LIKE '%premium%' OR plan_key LIKE '%ultimate%';

UPDATE recharge_orders SET points_validity_days = 0;

INSERT INTO subscription_plans (plan_key, plan_type, plan_name, duration_months, original_price, discount_price, points_included, points_validity_days, features, display_order) VALUES
('free_monthly', 'free', '免费版', 1, 0.00, 0.00, 50, 30, '{"monthly_points": 50, "ai_requests": "limited", "exports": "limited"}', 1),
('pro_monthly', 'pro', 'Pro版月度', 1, 9.90, 9.90, 600, 30, '{"monthly_points": 600, "ai_requests": "unlimited", "exports": "unlimited", "priority_support": true}', 2),
('max_monthly', 'max', 'Max版月度', 1, 29.90, 29.90, 2000, 30, '{"monthly_points": 2000, "ai_requests": "unlimited", "exports": "unlimited", "priority_support": true, "advanced_features": true}', 3),
('pro_yearly', 'pro', 'Pro版年度', 12, 118.80, 99.00, 7200, 365, '{"monthly_points": 600, "ai_requests": "unlimited", "exports": "unlimited", "priority_support": true}', 4),
('max_yearly', 'max', 'Max版年度', 12, 358.80, 299.00, 24000, 365, '{"monthly_points": 2000, "ai_requests": "unlimited", "exports": "unlimited", "priority_support": true, "advanced_features": true}', 5)
ON DUPLICATE KEY UPDATE 
plan_type = VALUES(plan_type),
points_validity_days = VALUES(points_validity_days);
