# 手机验证码登录功能部署指南

## 🚀 部署前准备

### 1. 阿里云短信服务配置

#### 获取AccessKey
1. 登录 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 点击右上角头像 → "AccessKey管理"
3. 创建AccessKey，记录 `AccessKey ID` 和 `AccessKey Secret`

#### 配置短信服务
1. 开通短信服务并完成实名认证
2. 申请短信签名（如：万来云边）
3. 申请验证码模板：`您的验证码是${code}，5分钟内有效。`
4. 记录模板CODE（如：SMS_324480375）

### 2. 环境变量配置

在 `.env` 文件中配置：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=loomrun

# JWT配置
JWT_SECRET=your-secret-key-2024

# 阿里云短信服务配置（必须配置）
ALIYUN_ACCESS_KEY_ID=your_actual_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_actual_access_key_secret
ALIYUN_SMS_SIGN_NAME=万来云边
ALIYUN_SMS_TEMPLATE_CODE=SMS_324480375
```

## 📦 安装和启动

### 1. 安装依赖

```bash
npm install
```

### 2. 初始化数据库

```bash
npm run db:init
```

### 3. 测试短信服务

```bash
npm run test:sms
```

### 4. 启动项目

```bash
npm run dev
```

## 🧪 功能测试

### 1. 测试发送验证码

```bash
curl -X POST http://localhost:3141/api/auth/send-code \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000"}'
```

### 2. 测试登录

```bash
curl -X POST http://localhost:3141/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"type":"phone","phone":"13800138000","code":"123456"}'
```

## 🔧 生产环境配置

### 1. 安全配置

```env
NODE_ENV=production
JWT_SECRET=your-very-secure-secret-key
```

### 2. 数据库优化

- 使用连接池
- 配置读写分离
- 设置适当的索引

### 3. 短信服务优化

- 设置更严格的频率限制
- 配置费用预警
- 监控发送成功率

## 📊 监控和日志

### 1. 短信发送监控

在阿里云控制台查看：
- 发送成功率
- 费用统计
- 错误日志

### 2. 应用日志

关键日志点：
- 验证码发送成功/失败
- 用户登录成功/失败
- API调用频率

## 🛡️ 安全建议

### 1. 验证码安全
- 5分钟有效期
- 一次性使用
- 频率限制（1分钟1次）

### 2. API安全
- 手机号格式验证
- 请求频率限制
- IP白名单（可选）

### 3. 数据安全
- 敏感信息加密存储
- 定期清理过期验证码
- 用户数据脱敏

## 🚨 故障排除

### 常见问题

1. **短信发送失败**
   - 检查AccessKey配置
   - 确认签名和模板已审核
   - 检查账户余额

2. **验证码验证失败**
   - 检查时间同步
   - 确认验证码未过期
   - 检查数据库连接

3. **登录失败**
   - 检查JWT配置
   - 确认Cookie设置
   - 检查用户表结构

### 调试命令

```bash
# 测试短信服务
npm run test:sms

# 检查数据库连接
npm run db:init

# 查看应用日志
npm run dev
```

## 📈 性能优化

### 1. 数据库优化
- 添加适当索引
- 定期清理过期数据
- 使用连接池

### 2. 缓存策略
- Redis缓存验证码
- 用户会话缓存
- API响应缓存

### 3. 短信优化
- 批量发送
- 模板缓存
- 错误重试机制

## 🔄 维护建议

### 日常维护
- 监控短信费用
- 清理过期验证码
- 更新AccessKey

### 定期检查
- 安全漏洞扫描
- 性能监控
- 用户反馈处理

---

## 📞 技术支持

如遇问题，请检查：
1. 环境变量配置
2. 阿里云服务状态
3. 数据库连接
4. 网络连接

运行 `npm run test:sms` 进行完整的配置检查。
