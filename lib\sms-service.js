const Dysmsapi20170525 = require('@alicloud/dysmsapi20170525').default;
const OpenApi = require('@alicloud/openapi-client');
const Util = require('@alicloud/tea-util');
const Credential = require('@alicloud/credentials');

// 从环境变量获取配置
const getSmsConfig = () => {
  const config = {
    accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET || '',
    signName: process.env.ALIYUN_SMS_SIGN_NAME || '万来云边',
    templateCode: process.env.ALIYUN_SMS_TEMPLATE_CODE || 'SMS_324480375',
    endpoint: 'dysmsapi.aliyuncs.com'
  };

  // 验证必要的配置
  if (!config.accessKeyId || !config.accessKeySecret) {
    throw new Error('阿里云短信服务配置不完整，请检查环境变量 ALIYUN_ACCESS_KEY_ID 和 ALIYUN_ACCESS_KEY_SECRET');
  }

  return config;
};

// 创建阿里云短信客户端
const createSmsClient = () => {
  const config = getSmsConfig();
  
  // 使用凭据初始化账号Client
  const credential = new Credential.default({
    type: 'access_key',
    accessKeyId: config.accessKeyId,
    accessKeySecret: config.accessKeySecret,
  });

  const openApiConfig = new OpenApi.Config({
    credential: credential,
  });
  
  openApiConfig.endpoint = config.endpoint;
  
  return new Dysmsapi20170525(openApiConfig);
};

// 发送短信验证码
const sendSmsCode = async (phone, code) => {
  try {
    const config = getSmsConfig();
    const client = createSmsClient();
    
    const sendSmsRequest = new Dysmsapi20170525.SendSmsRequest({
      signName: config.signName,
      templateCode: config.templateCode,
      phoneNumbers: phone,
      templateParam: JSON.stringify({ code }),
      smsUpExtendCode: '',
    });

    const runtime = new Util.RuntimeOptions({});
    
    console.log(`正在发送短信验证码到 ${phone}，验证码: ${code}`);
    
    const response = await client.sendSmsWithOptions(sendSmsRequest, runtime);
    
    console.log('阿里云短信API响应:', {
      Code: response.body.code,
      Message: response.body.message,
      BizId: response.body.bizId,
      RequestId: response.body.requestId
    });

    if (response.body.code === 'OK') {
      return {
        success: true,
        message: '短信发送成功',
        bizId: response.body.bizId
      };
    } else {
      return {
        success: false,
        message: response.body.message || '短信发送失败'
      };
    }
  } catch (error) {
    console.error('发送短信验证码失败:', error);
    
    // 处理阿里云API错误
    if (error.data && error.data.Recommend) {
      console.log('阿里云错误诊断地址:', error.data.Recommend);
    }
    
    return {
      success: false,
      message: error.message || '短信发送失败，请稍后重试'
    };
  }
};

// 验证手机号格式
const validatePhoneNumber = (phone) => {
  // 中国大陆手机号正则表达式
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 生成6位数字验证码
const generateSmsCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 短信发送频率限制检查
const checkSmsRateLimit = async (phone) => {
  // 这里可以实现基于Redis或数据库的频率限制
  // 暂时返回允许发送
  return { canSend: true };
};

module.exports = {
  sendSmsCode,
  validatePhoneNumber,
  generateSmsCode,
  checkSmsRateLimit,
  getSmsConfig
};
