"use client";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { X, Phone, Timer, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import Image from "next/image";
import { useUser } from "@/loomrunhooks/useUser";

interface AuthModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  title?: string;
  description?: string;
}

// 微信图标组件
const WechatIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <Image
    src="/weixin.svg"
    alt="微信"
    width={16}
    height={16}
    className={className}
  />
);

export function AuthModal({
  open,
  onClose,
  onSuccess,
  title = "欢迎登录LoomRun",
  description = ""
}: AuthModalProps) {
  const { refreshUser } = useUser();
  const [currentStep, setCurrentStep] = useState<"phone" | "wechat">("phone");
  const [phone, setPhone] = useState("");
  const [code, setCode] = useState("");
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [phoneError, setPhoneError] = useState("");
  const [codeError, setCodeError] = useState("");
  const [testModeCode, setTestModeCode] = useState(""); // 测试模式显示的验证码

  // 倒计时逻辑
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // 重置状态
  const resetModal = () => {
    setCurrentStep("phone");
    setPhone("");
    setCode("");
    setCountdown(0);
    setLoading(false);
    setAgreedToTerms(false);
    setPhoneError("");
    setCodeError("");
    setTestModeCode(""); // 清除测试模式验证码
  };

  // 关闭模态框
  const handleClose = () => {
    resetModal();
    onClose();
  };

  // 验证手机号
  const validatePhone = (phoneNumber: string) => {
    if (!phoneNumber) {
      setPhoneError("请输入手机号");
      return false;
    }
    
    // 只允许输入数字
    if (!/^\d*$/.test(phoneNumber)) {
      setPhoneError("手机号只能包含数字");
      return false;
    }
    
    if (phoneNumber.length < 11) {
      setPhoneError("手机号必须为11位");
      return false;
    }
    
    if (phoneNumber.length > 11) {
      setPhoneError("手机号不能超过11位");
      return false;
    }
    
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phoneNumber)) {
      setPhoneError("请输入正确的手机号格式");
      return false;
    }
    
    setPhoneError("");
    return true;
  };

  // 处理手机号输入
  const handlePhoneChange = (value: string) => {
    // 只允许输入数字，最多11位
    const numericValue = value.replace(/\D/g, '').slice(0, 11);
    setPhone(numericValue);
    
    if (numericValue) {
      validatePhone(numericValue);
    } else {
      setPhoneError("");
    }
  };

  // 验证验证码
  const validateCode = (codeValue: string) => {
    if (!codeValue) {
      setCodeError("请输入验证码");
      return false;
    }
    
    if (!/^\d*$/.test(codeValue)) {
      setCodeError("验证码只能包含数字");
      return false;
    }
    
    if (codeValue.length !== 6) {
      setCodeError("验证码必须为6位");
      return false;
    }
    
    setCodeError("");
    return true;
  };

  // 处理验证码输入
  const handleCodeChange = (value: string) => {
    // 只允许输入数字，最多6位
    const numericValue = value.replace(/\D/g, '').slice(0, 6);
    setCode(numericValue);
    
    if (numericValue) {
      validateCode(numericValue);
    } else {
      setCodeError("");
    }
  };

  // 发送验证码
  const sendVerificationCode = async () => {
    if (!validatePhone(phone)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/auth/send-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ phone }),
      });

      const data = await response.json();

      if (data.success) {
        // 检查是否是测试模式（消息中包含验证码）
        const testCodeMatch = data.message.match(/\[测试模式: (\d{6})\]/);
        if (testCodeMatch) {
          setTestModeCode(testCodeMatch[1]);
          toast.success(`验证码发送成功 (测试模式: ${testCodeMatch[1]})`);
        } else {
          setTestModeCode("");
          toast.success("验证码发送成功");
        }
        setCountdown(60);
      } else {
        toast.error(data.message || "发送失败");
      }
    } catch (error) {
      console.error("发送验证码失败:", error);
      toast.error("发送失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  // 手机登录
  const handlePhoneLogin = async () => {
    const isPhoneValid = validatePhone(phone);
    const isCodeValid = validateCode(code);
    
    if (!isPhoneValid || !isCodeValid) {
      return;
    }

    if (!agreedToTerms) {
      toast.error("请先同意用户协议和隐私政策");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ type: "phone", phone, code }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success("登录成功");

        // 先关闭弹窗
        handleClose();

        // 刷新用户状态
        try {
          console.log('🔄 登录成功，刷新用户状态...');
          await refreshUser();
          console.log('✅ 用户状态刷新成功');

          // 调用成功回调
          onSuccess();
        } catch (refreshError) {
          console.error('❌ 刷新用户状态失败:', refreshError);
          // 即使刷新失败也调用成功回调，避免卡住
          onSuccess();
        }
      } else {
        toast.error(data.message || "登录失败");
      }
    } catch (error) {
      console.error("手机登录失败:", error);
      toast.error("登录失败，请重试");
    } finally {
      setLoading(false);
    }
  };



  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent 
        showCloseButton={false}
        className="max-w-sm mx-auto bg-background border border-border text-foreground rounded-lg shadow-2xl p-0 overflow-hidden"
      >
        <DialogTitle className="sr-only">{title}</DialogTitle>
        <DialogDescription className="sr-only">{description}</DialogDescription>
        
        {/* 头部 */}
        <div className="relative p-4 border-b border-border">
          <button
            onClick={handleClose}
            className="absolute top-3 right-3 w-8 h-8 rounded-full bg-secondary hover:bg-secondary/80 flex items-center justify-center transition-colors text-muted-foreground hover:text-foreground"
          >
            <X className="w-4 h-4" />
          </button>
          
          <div className="text-center">
            <h2 className="text-lg font-semibold text-foreground">{title}</h2>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-6">
          {/* 登录方式切换 */}
          <div className="flex mb-6 bg-secondary rounded-lg p-1">
            <button
              onClick={() => setCurrentStep("phone")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                currentStep === "phone"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              <Phone className="w-4 h-4 inline mr-2" />
              手机登录
            </button>
            <button
              onClick={() => setCurrentStep("wechat")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                currentStep === "wechat"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              <WechatIcon className="w-4 h-4 inline mr-2" />
              微信登录
            </button>
          </div>

          {/* 手机登录 */}
          {currentStep === "phone" && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  手机号
                </label>
                <div className="relative">
                  <Input
                    type="tel"
                    value={phone}
                    onChange={(e) => handlePhoneChange(e.target.value)}
                    placeholder="请输入11位手机号"
                    className={`w-full h-11 bg-background border-border text-foreground placeholder-muted-foreground focus:border-primary rounded-lg ${
                      phoneError ? "border-red-500 focus:border-red-500" : ""
                    }`}
                    maxLength={11}
                  />
                  {phoneError && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <AlertCircle className="w-4 h-4 text-red-500" />
                    </div>
                  )}
                </div>
                {phoneError && (
                  <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {phoneError}
                  </p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  验证码
                </label>
                <div className="flex gap-3">
                  <div className="flex-1 relative">
                    <Input
                      type="text"
                      value={code}
                      onChange={(e) => handleCodeChange(e.target.value)}
                      placeholder="请输入6位验证码"
                      className={`w-full h-11 bg-background border-border text-foreground placeholder-muted-foreground focus:border-primary rounded-lg ${
                        codeError ? "border-red-500 focus:border-red-500" : ""
                      }`}
                      maxLength={6}
                    />
                    {codeError && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <AlertCircle className="w-4 h-4 text-red-500" />
                      </div>
                    )}
                  </div>
                  <Button
                    onClick={sendVerificationCode}
                    disabled={countdown > 0 || loading || !phone || phoneError !== ""}
                    className="h-11 px-4 bg-secondary hover:bg-secondary/80 text-secondary-foreground border border-border rounded-lg font-medium min-w-[100px] transition-colors disabled:opacity-50"
                  >
                    {countdown > 0 ? (
                      <div className="flex items-center gap-1">
                        <Timer className="w-4 h-4" />
                        {countdown}s
                      </div>
                    ) : (
                      "发送验证码"
                    )}
                  </Button>
                </div>
                {codeError && (
                  <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {codeError}
                  </p>
                )}
                {/* 测试模式显示验证码 */}
                {testModeCode && (
                  <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                    <div className="flex items-center justify-between">
                      <p className="text-yellow-800 dark:text-yellow-200 text-xs flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        测试模式验证码: <span className="font-mono font-bold">{testModeCode}</span>
                      </p>
                      <button
                        onClick={() => {
                          setCode(testModeCode);
                          validateCode(testModeCode);
                        }}
                        className="text-yellow-800 dark:text-yellow-200 text-xs underline hover:no-underline"
                      >
                        快速填入
                      </button>
                    </div>
                  </div>
                )}
              </div>
              
              {/* 用户协议 */}
              <div className="flex items-start gap-2 mt-4">
                <Checkbox
                  id="terms"
                  checked={agreedToTerms}
                  onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
                  className="mt-0.5 border-border data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
                <label htmlFor="terms" className="text-sm text-muted-foreground leading-5">
                  我已阅读并同意
                  <button
                    type="button"
                    onClick={() => window.open('/Legal page/loomrun-terms-of-service.html', '_blank')}
                    className="text-primary hover:text-primary/80 mx-1 underline"
                  >
                    《用户协议》
                  </button>
                  和
                  <button
                    type="button"
                    onClick={() => window.open('/Legal page/loomrun-privacy-policy.html', '_blank')}
                    className="text-primary hover:text-primary/80 mx-1 underline"
                  >
                    《隐私政策》
                  </button>
                </label>
              </div>
              
              <Button
                onClick={handlePhoneLogin}
                disabled={!phone || !code || !agreedToTerms || loading || phoneError !== "" || codeError !== ""}
                className="w-full h-11 bg-primary hover:bg-primary/90 text-primary-foreground border-0 rounded-lg font-medium transition-colors disabled:opacity-50"
              >
                {loading ? "登录中..." : "登录"}
              </Button>
            </div>
          )}

          {/* 微信登录 */}
          {currentStep === "wechat" && (
            <div className="text-center space-y-4">
              <div className="w-56 h-56 mx-auto bg-secondary rounded-lg flex items-center justify-center border-2 border-dashed border-border">
                <div className="text-muted-foreground">
                  <WechatIcon className="w-16 h-16 mx-auto mb-2" />
                  <p className="text-sm">微信二维码</p>
                  <p className="text-xs text-muted-foreground/60 mt-1">
                    (演示模式)
                  </p>
                </div>
              </div>
              
              <div className="space-y-1">
                <p className="text-sm text-foreground font-medium">
                  微信扫码关注&ldquo;LoomRun&rdquo;快速登录
                </p>
                <p className="text-xs text-muted-foreground">
                  扫码表示同意
                  <button
                    type="button"
                    onClick={() => toast.info("用户协议功能开发中")}
                    className="text-primary hover:text-primary/80 mx-1 underline"
                  >
                    《用户协议》
                  </button>
                  及
                  <button
                    type="button"
                    onClick={() => toast.info("隐私政策功能开发中")}
                    className="text-primary hover:text-primary/80 mx-1 underline"
                  >
                    《隐私政策》
                  </button>
                </p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 