"use client";

import React, { useCallback, useRef, useEffect, useState, useContext } from 'react';
import { ChatMessage } from '@/lib/project-manager';
import { UserContext } from '@/components/contexts/user-context';
import { GhostLogo } from '@/components/ui/ghost-logo';

import classNames from 'classnames';



// 🎯 优化：智能滚动Hook（高性能版本）
const useAutoScroll = () => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollHeight = useRef<number>(0);
  const isScrolling = useRef<boolean>(false);

  // 🔧 高性能：使用RAF进行滚动优化
  const handleScroll = useCallback(() => {
    if (isScrolling.current || !containerRef.current) return;
    
    isScrolling.current = true;
    requestAnimationFrame(() => {
      if (!containerRef.current) {
        isScrolling.current = false;
        return;
      }
      
      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50;
      
      if (isAtBottom !== !isUserScrolling) {
        setIsUserScrolling(!isAtBottom);
      }
      
      // 🔧 只在需要时清除/设置定时器
      if (!isAtBottom && !isUserScrolling) {
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
        
        // 2秒后重新启用自动滚动（缩短时间提升响应性）
        scrollTimeoutRef.current = setTimeout(() => {
          setIsUserScrolling(false);
        }, 2000);
      }
      
      isScrolling.current = false;
    });
  }, [isUserScrolling]);

  // 🎯 高性能：智能滚动到底部（只在真正需要时滚动）
  const scrollToBottom = useCallback((force = false) => {
    if (!messagesEndRef.current || !containerRef.current) return;
    
    const container = containerRef.current;
    const currentScrollHeight = container.scrollHeight;
    
    // 🔧 性能优化：检查内容是否真的发生了变化
    if (!force && !isUserScrolling && lastScrollHeight.current === currentScrollHeight) {
      return;
    }
    
    lastScrollHeight.current = currentScrollHeight;
    
    if (isUserScrolling && !force) return;
    
    // 🎯 使用更高效的滚动方式
    requestAnimationFrame(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
        });
      }
    });
  }, [isUserScrolling]);

  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return {
    messagesEndRef,
    containerRef,
    handleScroll,
    scrollToBottom,
    isUserScrolling,
  };
};

// 🎯 专业任务列表监控组件 - 类似Cursor TODO列表的感觉，丝滑划线删除动画
const TaskListMonitor = React.memo(({ 
  isGenerating = false,
  requestType = 'POST', // POST: 新建项目, PUT: 修改项目
  onTaskProgressChange // 新增：任务进度变化回调
}: { 
  isGenerating?: boolean;
  requestType?: 'POST' | 'PUT';
  onTaskProgressChange?: (currentTask: number, completedTasks: number[], isCollapsed: boolean, shouldShowCard?: boolean) => void;
}) => {
  const [currentTaskIndex, setCurrentTaskIndex] = useState(0);
  const [completedTasks, setCompletedTasks] = useState<number[]>([]);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [strikethroughTasks, setStrikethroughTasks] = useState<number[]>([]); // 新增：正在划线的任务
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // POST请求的专业任务列表（专业版）
  const postTasks = React.useMemo(() => [
    { id: 0, text: "深度分析用户需求，构建项目架构蓝图", phase: "正在分析需求" },
    { id: 1, text: "制定UI/UX设计方案，优化用户交互体验", phase: "开始设计方案" },
    { id: 2, text: "构建响应式布局框架，确保跨设备兼容性", phase: "设计布局框架" },
    { id: 3, text: "优化性能算法，集成现代化开发技术栈", phase: "优化性能架构" },
    { id: 4, text: "配置开发环境，初始化代码生成引擎", phase: "准备生成代码" }
  ], []);

  // PUT请求的简单修改状态
  const putTasks = React.useMemo(() => [
    { id: 0, text: "正在修改代码", phase: "正在修改代码" }
  ], []);

  const tasks = requestType === 'POST' ? postTasks : putTasks;

    // 🎯 丝滑任务列表动画逻辑 - 先显示所有任务，然后逐个划线删除
  useEffect(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (!isGenerating) {
      setCurrentTaskIndex(0);
      setCompletedTasks([]);
      setStrikethroughTasks([]);
      setIsCollapsed(false);
      return;
    }

    if (requestType === 'POST') {
      // 🎯 POST请求：丝滑的任务完成动画
      let taskIndex = 0;
      
      const completeNextTask = () => {
        if (taskIndex < tasks.length && isGenerating) {
          // 🎯 第一步：添加划线动画
          setStrikethroughTasks(prev => [...prev, taskIndex]);
          
          // 🎯 第二步：500ms后标记为完成并移除（增加仪式感）
          setTimeout(() => {
            setCompletedTasks(prev => {
              const newCompletedTasks = [...prev, taskIndex];
              
              // 🎯 关键：最后一个任务（"准备生成代码"）完成后立即收起并切换状态
              if (taskIndex === tasks.length - 1) {
                // 通知父组件显示卡片 - 延迟到下一个渲染周期
                setTimeout(() => {
                  if (onTaskProgressChange) {
                    onTaskProgressChange(taskIndex + 1, newCompletedTasks, false, true);
                  }
                }, 0);
                
                // 稍微延迟收起，增加装逼效果
                setTimeout(() => {
                  setIsCollapsed(true);
                }, 960); // 0.96秒后收起，减少20%时间
              } else {
                // 通知父组件 - 延迟到下一个渲染周期
                setTimeout(() => {
                  if (onTaskProgressChange) {
                    onTaskProgressChange(taskIndex + 1, newCompletedTasks, false);
                  }
                }, 0);
              }
              
              return newCompletedTasks;
            });
            setCurrentTaskIndex(taskIndex + 1);
          }, 500); // 增加到500ms，更有质感
          
          taskIndex++;
          
          if (taskIndex < tasks.length) {
            // 🎯 装逼节奏：1.6-2.8秒，减少20%时间
            const delay = Math.random() * 1200 + 1600;
            intervalRef.current = setTimeout(completeNextTask, delay);
          }
        }
      };

      // 🎯 稍微延迟开始第一个任务，增加悬念感
      intervalRef.current = setTimeout(completeNextTask, 800); // 0.8秒后开始，减少20%时间
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isGenerating, requestType, tasks.length, onTaskProgressChange]);

  // PUT请求的简单显示
  if (requestType === 'PUT') {
    return (
      <div className="p-3 bg-gray-100 dark:bg-neutral-800/50 border border-gray-200 dark:border-neutral-600/30 rounded-lg">
        <div className="flex items-center gap-3">
          <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700 dark:text-neutral-300">正在修改代码</span>
            <div className="flex gap-1">
              <div className="w-1 h-1 bg-gray-400 dark:bg-neutral-400 rounded-full animate-bounce"></div>
              <div className="w-1 h-1 bg-gray-400 dark:bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-1 h-1 bg-gray-400 dark:bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // POST请求的专业任务列表显示
  return (
    <div className="space-y-3">
      {/* 任务列表 */}
      <div className={`transition-all duration-500 ${isCollapsed ? 'max-h-0 overflow-hidden opacity-0' : 'max-h-96 opacity-100'}`}>
        <div className="bg-gray-100 dark:bg-neutral-800/50 border border-gray-200 dark:border-neutral-600/30 rounded-lg p-4">
          <div className="space-y-3">
                                      {tasks.map((task, index) => {
               const isCompleted = completedTasks.includes(index);
               const isCurrent = index === currentTaskIndex && !isCompleted;
               const isStrikethrough = strikethroughTasks.includes(index); // 使用划线状态

               return (
                 <div key={task.id} className="flex items-start gap-3">
                   {/* 状态图标 */}
                   <div className="flex-shrink-0 w-5 h-5 mt-0.5 flex items-center justify-center">
                     {isCompleted ? (
                       <div className="w-4 h-4 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg shadow-green-500/30 animate-[checkPulse_0.6s_ease-out]">
                         <svg className="w-2.5 h-2.5 text-white drop-shadow-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                           <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                         </svg>
                       </div>
                     ) : isCurrent ? (
                       <div className="w-4 h-4 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full animate-pulse shadow-lg shadow-blue-500/30"></div>
                     ) : (
                       <div className="w-4 h-4 border-2 border-neutral-500 rounded-full opacity-60"></div>
                     )}
                   </div>
                   
                   {/* 任务内容 */}
                   <div className="flex-1 min-w-0">
                     <div className={`flex items-center gap-2 transition-all duration-700 ${
                       isCompleted ? 'text-green-500 dark:text-green-400 opacity-70' : 
                       isCurrent ? 'text-blue-500 dark:text-blue-400 drop-shadow-sm' : 
                       'text-gray-600 dark:text-neutral-500 opacity-80'
                                            }`}>
                         <span className={`text-sm font-medium relative transition-all duration-300 ${
                           isStrikethrough ? 'text-gray-500 dark:text-neutral-600' : ''
                         }`}>
                           {task.text}
                           {/* 🎯 丝滑划线动画 - 只在划线状态时显示 */}
                           {isStrikethrough && (
                             <div className="absolute left-0 top-1/2 h-0.5 bg-gradient-to-r from-gray-400 to-gray-500 dark:from-neutral-400 dark:to-neutral-500 transform -translate-y-1/2 shadow-sm animate-[drawLine_600ms_ease-in-out_forwards] w-0"></div>
                           )}
                         </span>
                       {isCurrent && !isStrikethrough && (
                         <div className="flex gap-1 ml-2">
                           <div className="w-1 h-1 bg-blue-500 dark:bg-blue-400 rounded-full animate-bounce"></div>
                           <div className="w-1 h-1 bg-blue-500 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                           <div className="w-1 h-1 bg-blue-500 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                         </div>
                       )}
                     </div>
                   </div>
                 </div>
               );
             })}
          </div>
        </div>
      </div>
    </div>
  );
});

TaskListMonitor.displayName = 'TaskListMonitor';

// 🎯 优化：用户消息组件（防止重复渲染）- 现代化聊天布局 + v0.dev风格按钮
const UserMessage = React.memo(({ 
  message,
  onEdit,
  onDelete,
  onCopy
}: { 
  message: ChatMessage;
  onEdit?: (content: string) => void;
  onDelete?: (messageId: string) => void;
  onCopy?: (content: string) => void;
}) => {
  const { user } = useContext(UserContext);
  const [isHovered, setIsHovered] = useState(false);
  
  // 获取用户头像URL，优先使用avatar_url，其次是avatarUrl
  const avatarUrl = user?.avatar_url || user?.avatarUrl;
  
  // 🎯 按钮处理函数
  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit(message.content);
    }
  }, [onEdit, message.content]);
  
  const handleDelete = useCallback(() => {
    if (onDelete) {
      onDelete(message.id);
    }
  }, [onDelete, message.id]);
  
  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      if (onCopy) {
        onCopy(message.content);
      }
      // TODO: 可以添加复制成功的提示
    } catch (error) {
      console.error('复制失败:', error);
    }
  }, [message.content, onCopy]);
  
  return (
    <div 
      className="flex items-start gap-3 mb-4 message-wrapper justify-end group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 用户消息内容 - 在右边 */}
      <div className="flex-1 max-w-[80%] flex flex-col items-end">
        <div className="bg-gray-100 dark:bg-gradient-to-br dark:from-neutral-800 dark:to-neutral-900 text-gray-800 dark:text-neutral-100 px-4 py-3 rounded-2xl rounded-br-md shadow-lg border border-gray-200 dark:border-neutral-700/50 backdrop-blur-sm">
          {/* 显示图片 */}
          {message.images && message.images.length > 0 && (
            <div className="mb-3 flex flex-wrap gap-2">
              {message.images.map((imageUrl, index) => (
                <div key={index} className="relative">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={imageUrl}
                    alt={`用户上传图片 ${index + 1}`}
                    className="max-w-[200px] max-h-[150px] rounded-lg object-cover border border-neutral-600/50 shadow-md"
                  />
                </div>
              ))}
            </div>
          )}
          {/* 显示文本内容 */}
          <p className="text-sm leading-relaxed break-words">{message.content}</p>
        </div>
        
        {/* 🎯 v0.dev风格的操作按钮 */}
        <div className="flex items-center gap-1 mt-2 mr-1">
          {/* 编辑按钮 - 仅悬停时显示 */}
          <div className="relative group/edit">
            <button
              onClick={handleEdit}
              className={`p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-600/50 rounded-md transition-all duration-200 ${
                isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2 pointer-events-none'
              }`}
            >
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            {/* 专业Tooltip */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 bg-neutral-800 text-neutral-200 text-xs rounded-md shadow-lg border border-neutral-700 opacity-0 group-hover/edit:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              编辑消息
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-neutral-800"></div>
            </div>
          </div>
          
          {/* 删除按钮 - 仅悬停时显示 */}
          <div className="relative group/delete">
            <button
              onClick={handleDelete}
              className={`p-1.5 text-neutral-400 hover:text-red-400 hover:bg-red-500/10 rounded-md transition-all duration-200 ${
                isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2 pointer-events-none'
              }`}
            >
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
            {/* 专业Tooltip */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 bg-red-900/90 text-red-100 text-xs rounded-md shadow-lg border border-red-700 opacity-0 group-hover/delete:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              删除此消息及后续对话
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-red-900/90"></div>
            </div>
          </div>
          
          {/* 复制按钮 - 始终显示，在最右边 */}
          <div className="relative group/copy">
            <button
              onClick={handleCopy}
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-600/50 rounded-md transition-all duration-200"
            >
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>
            {/* 专业Tooltip */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 bg-neutral-800 text-neutral-200 text-xs rounded-md shadow-lg border border-neutral-700 opacity-0 group-hover/copy:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              复制消息
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-neutral-800"></div>
            </div>
          </div>
        </div>
        

      </div>
      
      {/* 用户头像 - 在右边，使用真实头像 */}
      <div className="user-avatar-container flex-shrink-0 w-8 h-8 rounded-full shadow-sm overflow-hidden bg-gray-200 dark:bg-neutral-700">
        {avatarUrl ? (
          /* eslint-disable-next-line @next/next/no-img-element */
          <img 
            src={avatarUrl} 
            alt="用户头像" 
            className="user-avatar-image w-full h-full object-cover rounded-full"
            onError={(e) => {
              // 头像加载失败时显示默认图标
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const parent = target.parentElement;
              if (parent) {
                parent.className = parent.className + ' avatar-fallback bg-gray-200 dark:bg-neutral-700 flex items-center justify-center';
                parent.innerHTML = `
                  <svg class="w-4 h-4 text-gray-600 dark:text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9C15 9.5 14.5 10 14 10H10C9.5 10 9 9.5 9 9V7.5L3 7V9C3 10 4 11 5 11V12.5C5 13.3 5.7 14 6.5 14S8 13.3 8 12.5V11H16V12.5C16 13.3 16.7 14 17.5 14S19 13.3 19 12.5V11C20 11 21 10 21 9Z"/>
                  </svg>
                `;
              }
            }}
          />
        ) : (
          <div className="avatar-fallback w-full h-full rounded-full bg-gray-200 dark:bg-neutral-700 flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600 dark:text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9C15 9.5 14.5 10 14 10H10C9.5 10 9 9.5 9 9V7.5L3 7V9C3 10 4 11 5 11V12.5C5 13.3 5.7 14 6.5 14S8 13.3 8 12.5V11H16V12.5C16 13.3 16.7 14 17.5 14S19 13.3 19 12.5V11C20 11 21 10 21 9Z"/>
            </svg>
          </div>
        )}
      </div>
    </div>
  );
});

UserMessage.displayName = 'UserMessage';

// 🎯 优化：AI消息组件（防止重复渲染）- 现代化聊天布局，分离卡片和完成文字
const AIMessage = React.memo(({ 
  message, 
  onCodeToggle,
  requestType = 'POST', // 新增：区分请求类型
  chatHistory = [] // 新增：聊天历史用于版本号计算
}: { 
  message: ChatMessage;
  onCodeToggle?: (code: string) => void;
  requestType?: 'POST' | 'PUT';
  chatHistory?: ChatMessage[];
}) => {
  // 🔧 每个消息独立的状态，不受其他消息影响
  const [localTaskPhase, setLocalTaskPhase] = useState('正在分析需求');
  // 移除复杂的真实代码生成检测，使用简化逻辑
  // 🔧 使用消息创建时保存的请求类型，确保每个消息的独立性
  const messageRequestType = React.useMemo(() => {
    // 🎯 关键修复：优先使用消息自带的requestType，确保状态不受后续操作影响
    if (message.requestType) {
      console.log('🔍 AIMessage: 使用消息保存的请求类型', {
        messageId: message.id,
        savedRequestType: message.requestType,
        timestamp: message.timestamp
      });
      return message.requestType;
    }
    
    // 🎯 兼容性：对于没有requestType的历史消息，使用传入的requestType
    console.log('🔍 AIMessage: 历史消息没有保存的requestType，使用传入值', {
      messageId: message.id,
      passedRequestType: requestType,
      timestamp: message.timestamp
    });
    return requestType;
  }, [message.requestType, message.id, message.timestamp, requestType]);

   // 🔧 优化：使用useCallback缓存处理函数
   const handleCodeToggle = React.useCallback(() => {
     if (onCodeToggle && message.htmlContent) {
       onCodeToggle(message.htmlContent);
     }
   }, [onCodeToggle, message.htmlContent]);
   


      // 移除复杂的真实代码检测逻辑，使用简化的任务列表控制

        // 🔧 简化的AI状态文字获取
     const getAIStatusText = () => {
       if (!message.isGenerating) {
         return '已完成';
       }
       
       if (messageRequestType === 'PUT') {
         return '正在修改代码';
       }
       
       // POST请求：直接使用本地任务阶段
       return localTaskPhase || '正在分析需求';
     };

   // 🔧 任务进度变化处理（装逼加强版）
   const handleTaskProgressChange = useCallback((currentTask: number, completedTasks: number[], isCollapsed: boolean, shouldShowCard?: boolean) => {
     const postTasks = [
       { phase: "正在分析需求" },
       { phase: "开始设计方案" },
       { phase: "设计布局框架" },
       { phase: "优化性能架构" },
       { phase: "准备生成代码" }
     ];
     
     // 🎯 如果任务列表收起了，直接切换到"正在生成代码"
     if (isCollapsed || shouldShowCard) {
       setLocalTaskPhase("正在生成代码");
       console.log('🎯 任务列表收起，切换到正在生成代码状态', message.id);
       return;
     }
     
     // 正常任务进度
     if (currentTask < postTasks.length) {
       setLocalTaskPhase(postTasks[currentTask].phase);
     }
     
     console.log('任务进度变化:', { 
       currentTask, 
       phase: postTasks[currentTask]?.phase,
       isCollapsed, 
       shouldShowCard, 
       messageId: message.id 
     });
   }, [message.id]);

     // 🎯 简化版本号逻辑
   const getVersionNumber = () => {
     // 🎯 优先使用消息自带的版本号
     if (message.versionNumber) {
       return message.versionNumber;
     }
     
     // 🎯 兼容性：POST请求永远是V1
     if (messageRequestType === 'POST') {
       return 1; // 新建项目永远是V1
     }
     
     // PUT请求：统计聊天历史中PUT请求的数量+1作为版本号
     const putRequestCount = chatHistory.filter(msg => 
       msg.type === 'ai' && msg.requestType === 'PUT'
     ).length;
     
     return putRequestCount + 2; // 从V2开始递增（V1是初始版本）
   };

  return (
    <>
      {/* AI头像和状态显示 */}
      <div className="flex items-start gap-3 mb-2 message-wrapper justify-start">
        {/* AI头像 - 在左边 */}
        <div className="flex-shrink-0 w-8 h-8 relative">
          <GhostLogo
            size={32}
            className="w-full h-full"
            isWorking={message.isGenerating}
          />
        </div>
        
        {/* AI状态显示 - 在右侧 */}
        <div className="flex-1 max-w-[80%]">
          <div className="mb-2">
            <div className={`text-sm font-medium mb-1 transition-all duration-300 ${
              message.isGenerating 
                ? 'bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-400 bg-clip-text text-transparent animate-pulse bg-[length:200%_100%] animate-[shimmer_2s_ease-in-out_infinite]' 
                : 'text-gray-700 dark:text-neutral-200'
            }`}>
              LoomRun - {getAIStatusText()}
            </div>
            {/* 🎯 三个点移到LoomR下方，改成蓝色，稍微大一点 */}
            {message.isGenerating && (
              <div className="flex gap-1.5 ml-0">
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce"></div>
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            )}
          </div>
          
                     {/* 任务监控组件 */}
           {message.isGenerating && (
             <TaskListMonitor 
               isGenerating={message.isGenerating}
               requestType={messageRequestType}
               onTaskProgressChange={handleTaskProgressChange}
             />
           )}
        </div>
      </div>

                   {/* 版本卡片 - 独立显示，POST和PUT请求都在有内容时显示 */}
      {(message.isGenerating || (!message.isGenerating && (message.htmlContent || message.content))) && (
        <div className="flex items-start gap-3 mb-2 message-wrapper justify-start">
          {/* 空白占位，保持对齐 */}
          <div className="flex-shrink-0 w-8 h-8"></div>
          
          {/* 版本卡片 - 整个卡片可点击 */}
          <div className="flex-1 max-w-[80%]">
            <div 
              className={`bg-white dark:bg-neutral-800 text-gray-800 dark:text-neutral-100 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm border border-gray-200 dark:border-neutral-700 transition-all duration-200 ${
                message.htmlContent ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-neutral-700 hover:border-gray-300 dark:hover:border-neutral-600 hover:shadow-md' : ''
              }`}
              onClick={message.htmlContent ? handleCodeToggle : undefined}
              title={message.htmlContent ? "点击查看此版本" : undefined}
            >
              {/* 版本号和状态 */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-blue-600 dark:text-cyan-400 text-sm font-mono font-medium bg-blue-100 dark:bg-cyan-400/10 px-2 py-1 rounded-full">
                    Version{getVersionNumber()}
                  </span>
                </div>
                
                {/* 视觉指示器 - 显示可点击状态 */}
                {message.htmlContent && (
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500 dark:text-neutral-500">点击查看</span>
                    <svg className="w-4 h-4 text-gray-400 dark:text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 完成消息 - 独立显示在聊天区 */}
      {!message.isGenerating && (message.htmlContent || message.content) && (
        <div className="flex items-start gap-3 mb-4 message-wrapper justify-start">
          {/* 空白占位，保持对齐 */}
          <div className="flex-shrink-0 w-8 h-8"></div>
          
          {/* 完成消息 */}
          <div className="flex-1 max-w-[80%]">
                                     <div className="text-sm text-gray-600 dark:text-neutral-400 flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-green-500/20 flex items-center justify-center">
                <svg className="w-2.5 h-2.5 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <span>
                {messageRequestType === 'POST' ? 
                  '项目已创建完成！点击上方卡片查看预览' :
                  '修改已完成！点击上方卡片查看效果'
                }
              </span>
            </div>
            

          </div>
        </div>
      )}
    </>
  );
}, (prevProps, nextProps) => {
  // 🔧 自定义比较函数：只在真正需要时重新渲染（包含消息的requestType以确保独立性）
  const shouldRerender = !(
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.isGenerating === nextProps.message.isGenerating &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.message.htmlContent === nextProps.message.htmlContent &&
    prevProps.message.versionNumber === nextProps.message.versionNumber &&
    prevProps.message.requestType === nextProps.message.requestType &&
    prevProps.requestType === nextProps.requestType &&
    prevProps.chatHistory?.length === nextProps.chatHistory?.length
  );
  
  if (shouldRerender) {
    console.log('🔄 AIMessage组件需要重新渲染:', {
      messageId: nextProps.message.id,
      changes: {
        id: prevProps.message.id !== nextProps.message.id,
        isGenerating: prevProps.message.isGenerating !== nextProps.message.isGenerating,
        content: prevProps.message.content !== nextProps.message.content,
        htmlContent: prevProps.message.htmlContent !== nextProps.message.htmlContent,
        versionNumber: prevProps.message.versionNumber !== nextProps.message.versionNumber
      },
      prevState: {
        isGenerating: prevProps.message.isGenerating,
        hasContent: !!prevProps.message.content,
        hasHtmlContent: !!prevProps.message.htmlContent
      },
      nextState: {
        isGenerating: nextProps.message.isGenerating,
        hasContent: !!nextProps.message.content,
        hasHtmlContent: !!nextProps.message.htmlContent
      }
    });
  }
  
  return !shouldRerender;
});

AIMessage.displayName = 'AIMessage';

// 🔧 删除重复的ChatMessage接口定义，使用lib/project-manager.ts中的统一定义

interface HtmlHistoryItem {
  html: string;
  createdAt: Date;
  prompt: string;
}

interface EnhancedChatProps {
  html: string;
  setHtml: (html: string) => void;
  htmlHistory: HtmlHistoryItem[];
  setHtmlHistory: (history: HtmlHistoryItem[]) => void;
  isAiWorking: boolean;
  setIsAiWorking: React.Dispatch<React.SetStateAction<boolean>>;
  isEditableModeEnabled: boolean;
  setIsEditableModeEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  selectedElement: HTMLElement | null;
  setSelectedElement: React.Dispatch<React.SetStateAction<HTMLElement | null>>;
  chatHistory: ChatMessage[];
  setChatHistory: React.Dispatch<React.SetStateAction<ChatMessage[]>>;
  onSwitchToEditor?: (htmlContent?: string) => void;
  onSuccess: (finalHtml: string, prompt: string) => void;
  onSwitchToPreview?: () => void;
  isStandalone?: boolean;
  completeAIGeneration?: (htmlContent: string) => void;
  stopAIGeneration?: (htmlContent?: string) => void;
  // 🔧 移除：统一输入框状态已不再需要

}

export const EnhancedChat: React.FC<EnhancedChatProps> = ({
  /* eslint-disable @typescript-eslint/no-unused-vars */
  html,
  setHtml,
  htmlHistory,
  setHtmlHistory,
  isAiWorking,
  setIsAiWorking,
  isEditableModeEnabled,
  setIsEditableModeEnabled,
  selectedElement,
  setSelectedElement,
  /* eslint-enable @typescript-eslint/no-unused-vars */
  chatHistory,
  setChatHistory,
  onSwitchToEditor,
  onSuccess,
  /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
  onSwitchToPreview,
  isStandalone = false,
  completeAIGeneration,
  /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
  stopAIGeneration,
}) => {
  // 🔧 调试：监控聊天历史变化
  React.useEffect(() => {
    console.log('🎯 EnhancedChat: 聊天历史变化', {
      totalMessages: chatHistory.length,
      messages: chatHistory.map((msg, index) => ({
        index,
        id: msg.id,
        type: msg.type,
        isGenerating: msg.isGenerating,
        hasContent: !!msg.content,
        hasHtmlContent: !!msg.htmlContent,
        versionNumber: msg.versionNumber,
        contentPreview: msg.content ? msg.content.substring(0, 30) + '...' : null
      })),
      timestamp: new Date().toISOString()
    });
    
    // 🔧 特别关注AI消息状态
    const aiMessages = chatHistory.filter(msg => msg.type === 'ai');
    if (aiMessages.length > 0) {
      console.log('🤖 EnhancedChat: AI消息详情', {
        aiMessageCount: aiMessages.length,
        lastAIMessage: aiMessages[aiMessages.length - 1] ? {
          id: aiMessages[aiMessages.length - 1].id,
          isGenerating: aiMessages[aiMessages.length - 1].isGenerating,
          hasContent: !!aiMessages[aiMessages.length - 1].content,
          hasHtmlContent: !!aiMessages[aiMessages.length - 1].htmlContent,
          versionNumber: aiMessages[aiMessages.length - 1].versionNumber
        } : null
      });
    }
  }, [chatHistory]);

  const { messagesEndRef, containerRef, handleScroll, scrollToBottom, isUserScrolling } = useAutoScroll();
  const [hasNewMessages, setHasNewMessages] = useState(false);
  
  // 🔧 监听聊天历史变化以控制滚动
  useEffect(() => {
    if (chatHistory.length > 0) {
      scrollToBottom();
    }
  }, [chatHistory, scrollToBottom]);

  // 智能自动滚动 - 只在特定情况下滚动
  useEffect(() => {
    if (chatHistory.length === 0) return;
    
    const lastMessage = chatHistory[chatHistory.length - 1];
    
    // 只在以下情况自动滚动到底部：
    // 1. 新消息正在生成中（AI回复）
    // 2. 刚添加了用户消息
    const shouldAutoScroll = 
      (lastMessage?.isGenerating) || // AI正在生成
      (lastMessage?.type === 'user' && Date.now() - lastMessage.timestamp.getTime() < 1000); // 刚发送的用户消息（1秒内）
    
    if (shouldAutoScroll) {
      // 延迟一点再滚动，让DOM更新完成
      setTimeout(() => scrollToBottom(false), 100);
      setHasNewMessages(false); // 清除新消息提示
    } else if (isUserScrolling && lastMessage && !lastMessage.isGenerating) {
      // 用户在查看历史时有新消息完成，显示提示
      setHasNewMessages(true);
    }
  }, [chatHistory, scrollToBottom, isUserScrolling]);

  // 手动滚动到底部并清除新消息提示
  const handleScrollToNewMessages = useCallback(() => {
    scrollToBottom(true);
    setHasNewMessages(false);
  }, [scrollToBottom]);

  // 🎯 用户消息操作处理函数
  const handleEditMessage = useCallback((content: string) => {
    // 🔧 消息编辑功能已移至ChatArea统一处理
    console.log('✅ EnhancedChat: 编辑消息功能已移至ChatArea', { content: content.substring(0, 50) + '...' });
  }, []);

  const handleDeleteMessage = useCallback(async (messageId: string) => {
    try {
      // 🎯 找到要删除的消息在聊天历史中的位置
      const messageIndex = chatHistory.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        console.error('❌ 未找到要删除的消息:', messageId);
        return;
      }

      // 🔧 删除该消息及其后续所有消息
      const messagesToKeep = chatHistory.slice(0, messageIndex);
      setChatHistory(messagesToKeep);
      
      console.log('✅ 本地聊天历史已更新', {
        原始消息数: chatHistory.length,
        保留消息数: messagesToKeep.length,
        删除的消息数: chatHistory.length - messagesToKeep.length
      });

      // 🔧 如果不是独立模式（即在项目编辑器中），需要删除数据库中的对话
      if (!isStandalone) {
        const projectId = window.location.pathname.split('/')[2];
        if (projectId) {
          // 🎯 调用API删除数据库中该消息之后的所有对话
          const response = await fetch(`/api/me/projects/${projectId}/chat`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              fromMessageId: messageId,
              // 可以传递时间戳作为备用标识
              fromTimestamp: chatHistory[messageIndex].timestamp
            }),
          });

          if (response.ok) {
            console.log('✅ 数据库中的后续对话已删除');
          } else {
            console.error('❌ 删除数据库对话失败', response.status);
          }
        }
      }
    } catch (error) {
      console.error('❌ 删除消息时发生错误:', error);
    }
  }, [chatHistory, setChatHistory, isStandalone]);

  const handleCopyMessage = useCallback((content: string) => {
    // 复制成功的反馈可以在这里处理
    console.log('✅ 消息已复制到剪贴板:', content.substring(0, 50) + '...');
    // TODO: 可以添加一个临时的成功提示
  }, []);

  // 🔧 成功处理函数（优化版）
  /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
  const handleSuccess = useCallback((finalHtml: string, prompt: string) => {
    console.log('🎯 EnhancedChat: AI生成成功', {
      htmlLength: finalHtml.length,
      prompt: prompt.substring(0, 50) + '...',
      isStandalone,
      chatHistoryLength: chatHistory.length
    });
    
    // 更新HTML状态
    setHtml(finalHtml);
    
    // 更新HTML历史记录
    const currentHistory = [...htmlHistory, {
      html: finalHtml,
      createdAt: new Date(),
      prompt: prompt,
    }];
    setHtmlHistory(currentHistory);
    setSelectedElement(null);
    
    // 🔧 关键修复：只在POST请求时更新聊天历史，PUT请求已在chat-area中处理
    if (isStandalone) {
      // POST请求：新建项目，在这里更新聊天历史
      console.log('🔄 EnhancedChat: POST请求完成，更新AI消息状态');
      setChatHistory(prev => {
        const updated = [...prev];
        const lastMessage = updated[updated.length - 1];
        
        console.log('🔍 EnhancedChat: 检查最后一条消息', {
          hasLastMessage: !!lastMessage,
          messageType: lastMessage?.type,
          isGenerating: lastMessage?.isGenerating,
          messageId: lastMessage?.id
        });
        
        // 只有当最后一条消息是正在生成的AI消息时才更新
        if (lastMessage && lastMessage.type === 'ai' && lastMessage.isGenerating === true) {
          console.log('✅ EnhancedChat: 找到正在生成的AI消息，开始更新状态');
          
          // 🔧 创建新的消息对象确保React检测到变化
          const updatedMessage = {
            ...lastMessage,
            isGenerating: false,
            content: '',
            htmlContent: finalHtml,
            timestamp: new Date()
          };
          
          // 替换最后一条消息
          updated[updated.length - 1] = updatedMessage;
          
          console.log('✅ EnhancedChat: AI消息状态已更新为完成', {
            messageId: updatedMessage.id,
            isGenerating: updatedMessage.isGenerating,
            hasHtmlContent: !!updatedMessage.htmlContent,
            htmlContentLength: updatedMessage.htmlContent?.length || 0
          });
        } else {
          console.log('⚠️ EnhancedChat: 未找到正在生成的AI消息或已经完成', {
            lastMessageType: lastMessage?.type,
            isGenerating: lastMessage?.isGenerating,
            totalMessages: updated.length
          });
        }
        return updated;
      });
    } else {
      // PUT请求：修改项目，聊天历史已在chat-area中处理，这里不再重复更新
      console.log('🔄 EnhancedChat: PUT请求完成，聊天历史由chat-area处理');
    }
    
    // 🔧 如果有completeAIGeneration方法，也调用它（兼容性）
    if (completeAIGeneration) {
      console.log('🔧 EnhancedChat: 调用completeAIGeneration方法');
      completeAIGeneration(finalHtml);
    }
    
    // 调用父组件的onSuccess
    onSuccess(finalHtml, prompt);
  }, [setHtml, htmlHistory, setHtmlHistory, setSelectedElement, completeAIGeneration, setChatHistory, onSuccess, isStandalone, chatHistory.length]);

  // 🔧 移除：handleAIRequest函数已不再需要，所有AI请求统一由ChatArea的handleUnifiedAI处理

  // 🔧 移除：handleNewPrompt函数已不再需要，统一由ChatArea的handleUnifiedAI处理

  return (
    <div
      className={classNames(
        "chat-container flex flex-col enhanced-chat",
        {
          "w-full flex-1 min-h-0": !isStandalone, // 使用flex-1和min-h-0确保正确的高度分配
          "w-full bg-neutral-900/70 border border-neutral-700 rounded-3xl shadow-2xl p-6 max-h-[75vh] min-h-[60vh]": isStandalone,
        }
      )}
    >
      {/* 聊天历史记录 */}
      <div 
        ref={containerRef}
        className="flex-1 overflow-y-auto p-4 min-h-0 max-h-full chat-scrollbar relative"
        onScroll={handleScroll}
      >
        {chatHistory.length === 0 ? (
          <div className="flex-1 flex flex-col items-center justify-center text-center text-gray-500 dark:text-neutral-400 px-8">
            <div className="w-12 h-12 mb-6 relative">
              <GhostLogo
                size={48}
                className="w-full h-full"
              />
            </div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-neutral-200">与 LoomRun AI 对话</h3>
            <p className="text-sm max-w-sm text-gray-600 dark:text-neutral-400">告诉AI您想要什么样的网站，我们会为您生成专业的代码</p>
            <div className="flex items-center gap-2 mt-4 text-xs text-gray-500 dark:text-neutral-500">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>AI助手已就绪</span>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            {chatHistory.map((message) => (
              message.type === 'user' ? (
                <UserMessage 
                  key={message.id} 
                  message={message}
                  onEdit={handleEditMessage}
                  onDelete={handleDeleteMessage}
                  onCopy={handleCopyMessage}
                />
              ) : (
                <AIMessage 
                  key={message.id} 
                  message={message}
                  onCodeToggle={(code) => onSwitchToEditor?.(code)}
                  requestType={message.requestType || 'POST'}
                  chatHistory={chatHistory}
                />
              )
            ))}
          </div>
        )}

        {/* 自动滚动锚点 */}
        <div ref={messagesEndRef} />
      </div>

      {/* 新消息提示按钮 - 使用相对定位 */}
      {hasNewMessages && (
        <div className="flex justify-end p-4">
          <button
            onClick={handleScrollToNewMessages}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-full shadow-lg flex items-center gap-2 animate-bounce"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
            新消息
          </button>
        </div>
      )}

      {/* 🔧 关键修复：移除重复的输入框，统一使用ChatArea中的handleUnifiedAI逻辑 */}
      {/* 聊天模式的输入框已移除，统一由ChatArea管理 */}

    </div>
  );
};