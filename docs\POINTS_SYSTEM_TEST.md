# 积分系统测试指南

## 🎯 测试目标

验证新用户注册送积分功能是否正常工作，包括：
- 积分分类和有效期管理
- 积分余额记录创建
- 积分交易记录生成
- 用户积分统计更新

## 🛠️ 测试准备

### 1. 确保数据库已更新
运行以下SQL文件更新数据库结构：
```bash
# 在Navicat中依次运行：
1. scripts/add_points_system.sql
2. scripts/points_expiry_system.sql
3. scripts/insert_default_data.sql
```

### 2. 检查系统设置
确保以下系统设置已正确配置：
- `new_user_points_enabled`: true
- `new_user_points_amount`: 100
- `activity_points_validity_days`: 30

## 🧪 测试方法

### 方法一：使用测试脚本（推荐）
```bash
# 在项目根目录运行
node scripts/test-points-system.js
```

### 方法二：使用前端测试页面
1. 启动开发服务器：`npm run dev`
2. 访问：`http://localhost:3000/test-points`
3. 登录后点击"发放新用户积分"按钮
4. 点击"获取积分历史"查看记录

### 方法三：使用API接口
```bash
# 发放积分
curl -X POST http://localhost:3000/api/test/grant-points \
  -H "Content-Type: application/json" \
  -b "loomrun_token=your_token"

# 查看积分历史
curl http://localhost:3000/api/points/history \
  -b "loomrun_token=your_token"
```

## ✅ 预期结果

### 成功场景
1. **首次发放**：
   - 返回成功消息
   - 用户积分增加100
   - 创建积分余额记录（activity类型，30天有效期）
   - 创建积分交易记录
   - 更新用户统计信息

2. **重复发放**：
   - 返回"用户已获得过注册积分"消息
   - 积分不会重复发放

### 数据库变化
```sql
-- 用户表更新
UPDATE users SET 
  points = points + 100,
  total_earned_points = total_earned_points + 100
WHERE id = user_id;

-- 创建积分余额记录
INSERT INTO user_points_balance (
  user_id, points_type, points_amount, expires_at
) VALUES (
  user_id, 'activity', 100, DATE_ADD(NOW(), INTERVAL 30 DAY)
);

-- 创建交易记录
INSERT INTO points_transactions (
  user_id, transaction_type, points_amount, 
  source_type, points_type, expires_at, description
) VALUES (
  user_id, 'earn', 100, 
  'registration', 'activity', expires_at, '新用户注册奖励 100 积分'
);
```

## 🔍 验证检查

### 1. 用户菜单积分显示
- 顶部用户菜单应显示积分卡片
- 当前积分：100
- 累计获得：100
- 已消费：0

### 2. 数据库记录
```sql
-- 检查用户积分
SELECT points, total_earned_points, total_spent_points 
FROM users WHERE id = user_id;

-- 检查积分余额
SELECT * FROM user_points_balance 
WHERE user_id = user_id AND points_type = 'activity';

-- 检查交易记录
SELECT * FROM points_transactions 
WHERE user_id = user_id AND source_type = 'registration';
```

### 3. 日志输出
控制台应显示：
```
🎉 新用户 {userId} 获得注册奖励 100 积分，有效期至 {date}
✅ 积分交易成功: 用户{userId} 获得 100 activity积分 (有效期至: {date})
```

## 🚨 常见问题

### 1. 积分未发放
- 检查系统设置 `new_user_points_enabled` 是否为 true
- 检查用户是否已获得过注册积分
- 查看控制台错误日志

### 2. 数据库错误
- 确保所有积分相关表已创建
- 检查外键约束是否正确
- 验证字段类型和长度

### 3. 前端显示问题
- 刷新用户信息：调用 `refreshUser()`
- 检查 `/api/me` 接口是否返回积分字段
- 验证用户菜单组件是否正确渲染

## 📊 测试数据示例

成功的积分发放应产生以下数据：

```json
{
  "user": {
    "id": 1,
    "points": 100,
    "total_earned_points": 100,
    "total_spent_points": 0
  },
  "balance_record": {
    "user_id": 1,
    "points_type": "activity",
    "points_amount": 100,
    "expires_at": "2025-08-29T12:00:00.000Z"
  },
  "transaction": {
    "user_id": 1,
    "transaction_type": "earn",
    "points_amount": 100,
    "source_type": "registration",
    "points_type": "activity",
    "description": "新用户注册奖励 100 积分"
  }
}
```

## 🎉 测试完成

如果所有测试都通过，说明新用户注册送积分功能已成功实现！
