"use client";
import { useState, useRef, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useLocalStorage, useMount } from "react-use";
import { useHtmlEditor } from "@/loomrunhooks/useHtmlEditor";
import { useChatHistory } from "@/loomrunhooks/useChatHistory";
// useProject hook 已删除，功能已迁移到 ProjectManager

import { ChatArea } from "@/components/editor/chat-area";
import { PreviewArea } from "@/components/editor/preview-area";
import { Header } from "@/components/editor/header";
import { DeployButton } from "@/components/editor/deploy-button";
import { ProjectSidebar } from "@/components/project-sidebar";
import { WelcomeLayout } from "@/components/layouts/welcome-layout";
import { ProjectProvider } from "@/components/contexts/project-context";
import { CommunityPage } from "@/components/community/community-page";
import { ResizableSplitter } from "@/components/ui/resizable-splitter";
import { AuthModal } from "@/components/auth-modal";

import { Project } from "@/types";
import { ChatMessage } from "@/lib/project-manager";
import { defaultHTML } from "@/lib/consts";
import { PerformanceLogger } from "@/lib/performance-logger";
import { ProjectManager } from "@/lib/project-manager";
import { globalAIState, GlobalAIState } from "@/lib/global-ai-state";
import { useUser } from "@/loomrunhooks/useUser";
import { toast } from "sonner";

interface ProjectItem {
  id: number;
  title: string;
  html_content: string;
  prompts: string[];
  created_at: string;
  updated_at: string;
}

// 🔧 删除重复的ExtendedChatMessage接口，使用统一的ChatMessage

export const EditorLayout = ({ project }: { project?: Project | null }) => {
  const [htmlStorage, , removeHtmlStorage] = useLocalStorage("html_content");
  const router = useRouter();
  
  const initialHtml = project?.html_content || (htmlStorage as string) || defaultHTML;
  
  const {
    html,
    setHtml,
    htmlHistory,
    setHtmlHistory,
    prompts,
    setPrompts,
  } = useHtmlEditor(initialHtml);

  // 使用自定义hooks管理状态
  const { user } = useUser();
  const chatHistoryHook = useChatHistory();
  // projectHook 已删除，功能已迁移到 ProjectManager

  // 界面状态
  const [layoutMode, setLayoutMode] = useState<'welcome' | 'generation' | 'editing'>('welcome');
  const [isProjectSwitching, setIsProjectSwitching] = useState(false);
  const [isAiWorking, setIsAiWorking] = useState(false);
  const [isEditableModeEnabled, setIsEditableModeEnabled] = useState(false);
  const [selectedElement, setSelectedElement] = useState<HTMLElement | null>(null);
  const [currentVersionNumber, setCurrentVersionNumber] = useState<number>(1);
  
  // 🎯 拖拽分隔条状态
  const [chatAreaWidth, setChatAreaWidth] = useState<number>(20); // 默认20%宽度，在17%-65%范围内
  
  // 🎯 持久化用户的布局偏好
  useEffect(() => {
    // 🎯 如果是项目模式，始终使用默认位置20%，不继承之前的偏好
    if (project) {
      setChatAreaWidth(20);
      return;
    }
    
    // 🎯 只有在非项目模式下才使用保存的布局偏好
    const savedWidth = localStorage.getItem('loomrun-chat-width');
    if (savedWidth) {
      const width = parseFloat(savedWidth);
      // 确保保存的宽度在有效范围内，否则使用默认值20%
      if (width >= 17 && width <= 65) {
        setChatAreaWidth(width);
      } else {
        // 清除无效的保存值，使用默认值
        localStorage.removeItem('loomrun-chat-width');
        setChatAreaWidth(20);
      }
    }
    // 如果没有保存的值，确保使用默认值20%
    else {
      setChatAreaWidth(20);
    }
  }, [project]); // 依赖project，确保项目变化时重新执行
  
  // 🎯 保存用户的布局偏好
  const handleWidthChange = useCallback((newWidth: number) => {
    setChatAreaWidth(newWidth);
    // 🎯 只有在非项目模式下才保存布局偏好，项目模式始终使用默认值
    if (!project) {
      localStorage.setItem('loomrun-chat-width', newWidth.toString());
    }
  }, [project]);
  
  // 🎯 社区页面状态
  const [isCommunityPageMode, setIsCommunityPageMode] = useState(false);

  // 🔐 登录弹窗状态
  const [showLoginModal, setShowLoginModal] = useState(false);
  
  // 🎯 项目状态更新监听 - 监听项目创建事件
  const [currentProject, setCurrentProject] = useState<Project | null>(project || null);
  
  // 🎯 监听项目创建事件，实时更新项目状态
  useEffect(() => {
    const handleProjectCreated = (event: CustomEvent) => {
      const projectData = event.detail;
      if (projectData && projectData.projectId) {
        console.log('🎯 EditorLayout: 收到项目创建事件，更新项目状态', projectData);
        setCurrentProject({
          id: parseInt(projectData.projectId),
          title: projectData.title || `项目 ${projectData.projectId}`,
          html_content: projectData.htmlContent || '',
          prompts: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: projectData.userId || 0
        });
      }
    };

    // 监听自定义事件
    window.addEventListener('project-created', handleProjectCreated as EventListener);

    // 🔐 监听登录弹窗事件
    const handleShowLoginModal = () => {
      setShowLoginModal(true);
    };
    window.addEventListener('show-login-modal', handleShowLoginModal);

    // 监听localStorage事件
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'project-created-event' && e.newValue) {
        try {
          const projectData = JSON.parse(e.newValue);
          handleProjectCreated({ detail: projectData } as CustomEvent);
        } catch (error) {
          console.error('解析项目创建事件失败:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // 监听BroadcastChannel
    let channel: BroadcastChannel | null = null;
    if ('BroadcastChannel' in window) {
      channel = new BroadcastChannel('project-updates');
      channel.addEventListener('message', (event) => {
        if (event.data.type === 'project-created') {
          handleProjectCreated({ detail: event.data } as CustomEvent);
        }
      });
    }

    return () => {
      window.removeEventListener('project-created', handleProjectCreated as EventListener);
      window.removeEventListener('show-login-modal', handleShowLoginModal);
      window.removeEventListener('storage', handleStorageChange);
      if (channel) {
        channel.close();
      }
    };
  }, []);

  // 🎯 当props中的project变化时，同步更新currentProject
  useEffect(() => {
    if (project) {
      setCurrentProject(project);
    }
  }, [project]);
  
  // 🎯 统一的AI输入框状态
  const [sharedInputPrompt, setSharedInputPrompt] = useState<string>("");
  // 🎯 实时流式HTML内容
  const [streamingHtml, setStreamingHtml] = useState<string>("");
  // 🚀 全局AI状态
  const [globalState, setGlobalState] = useState<GlobalAIState | null>(null);
  const prevGlobalStateRef = useRef<GlobalAIState | null>(null);
  const streamingHtmlRef = useRef<string>('');
  const chatHistoryRef = useRef(chatHistoryHook.chatHistory);
  
  // 🔧 包装函数：同时更新state和ref
  const updateStreamingHtml = useCallback((newHtml: string) => {
    if (streamingHtmlRef.current !== newHtml) {
      streamingHtmlRef.current = newHtml;
      setStreamingHtml(newHtml);
    }
  }, []);
  
  // 🔧 关键修复：实时更新chatHistory的ref
  const chatHistoryHookRef = useRef(chatHistoryHook);
  useEffect(() => {
    chatHistoryRef.current = chatHistoryHook.chatHistory;
    chatHistoryHookRef.current = chatHistoryHook;
  }, [chatHistoryHook]);

  // 🔧 关键修复：专门监听AI生成完成事件
  useEffect(() => {
    const handleAIComplete = () => {
      const currentGlobalState = globalAIState.getState();
      console.log('🎯 EditorLayout: AI生成完成事件处理', {
        isGenerating: currentGlobalState.isGenerating,
        hasContent: !!currentGlobalState.accumulatedContent,
        chatHistoryLength: currentGlobalState.chatHistory.length,
        localChatHistoryLength: chatHistoryRef.current.length
      });
      
      if (!currentGlobalState.isGenerating && currentGlobalState.accumulatedContent) {
        console.log('🎯 EditorLayout: 专门监听到AI生成完成事件');
        
        // 🔧 关键修复：立即同步全局状态到本地聊天历史
        const globalChatHistory = currentGlobalState.chatHistory;
        const localChatHistory = chatHistoryRef.current;
        
        // 检查是否需要更新
        const needsUpdate = globalChatHistory.length !== localChatHistory.length ||
          (globalChatHistory.length > 0 && localChatHistory.length > 0 &&
           globalChatHistory[globalChatHistory.length - 1].isGenerating !== 
           localChatHistory[localChatHistory.length - 1].isGenerating);
        
        if (needsUpdate) {
          console.log('🔄 EditorLayout: 立即同步全局聊天历史到本地', {
            globalLength: globalChatHistory.length,
            localLength: localChatHistory.length,
            globalLastMessage: globalChatHistory.length > 0 ? {
              id: globalChatHistory[globalChatHistory.length - 1].id,
              isGenerating: globalChatHistory[globalChatHistory.length - 1].isGenerating,
              hasHtmlContent: !!globalChatHistory[globalChatHistory.length - 1].htmlContent
            } : null,
            localLastMessage: localChatHistory.length > 0 ? {
              id: localChatHistory[localChatHistory.length - 1].id,
              isGenerating: localChatHistory[localChatHistory.length - 1].isGenerating,
              hasHtmlContent: !!localChatHistory[localChatHistory.length - 1].htmlContent
            } : null
          });
          
          // 🔧 关键修复：强制更新本地聊天历史
          chatHistoryHookRef.current.setChatHistory([...globalChatHistory]);
          
          // 🔧 同时更新HTML内容
          if (currentGlobalState.accumulatedContent && setHtml) {
            setHtml(currentGlobalState.accumulatedContent);
          }
          
          console.log('✅ EditorLayout: 本地聊天历史已更新');
        }
      }
    };

    // 监听自定义事件
    const handleCustomEvent = (event: CustomEvent) => {
      console.log('🎯 EditorLayout: 收到自定义事件', event.detail);
      if (event.detail?.type === 'ai-generation-complete') {
        console.log('🎯 EditorLayout: 收到AI生成完成自定义事件');
        handleAIComplete();
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('ai-generation-complete', handleCustomEvent as EventListener);
      console.log('🔧 EditorLayout: 已注册ai-generation-complete事件监听器');
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('ai-generation-complete', handleCustomEvent as EventListener);
        console.log('🔧 EditorLayout: 已清理ai-generation-complete事件监听器');
      }
    };
  }, [setHtml]);

  // 🚀 项目栏悬浮状态
  const [isSidebarHovered, setIsSidebarHovered] = useState(false);
  const [isHoverTriggered, setIsHoverTriggered] = useState(false);
  const sidebarTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const triggerZoneRef = useRef<HTMLDivElement>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const mouseTrackingRef = useRef<boolean>(false);
  const lastMousePositionRef = useRef<{ x: number; y: number } | null>(null);
  
  // 🎯 新增：智能触发区域检测（避开底部50%区域，扩大禁用范围）
  const isInSmartTriggerZone = useCallback((clientX: number, clientY: number): boolean => {
    const TRIGGER_WIDTH = 2; // 精确匹配2px触发区域宽度
    const viewportHeight = window.innerHeight;
    const headerHeight = 44; // Header高度精确44px（h-11），与Header组件的h-11样式保持一致
    const availableHeight = viewportHeight - headerHeight;
    const excludeBottomHeight = availableHeight * 0.5; // 底部50%区域禁用
    const maxTriggerY = viewportHeight - excludeBottomHeight;
    
    // 🎯 条件：X坐标在2px触发区域内 && Y坐标不在底部50%区域
    return clientX <= TRIGGER_WIDTH && clientY >= headerHeight && clientY <= maxTriggerY;
  }, []);

  // 🔥 高效的侧边栏边界检测函数 - 精确检测项目栏实际内容右边框
  const isMouseCompletelyOutsideSidebar = useCallback((clientX: number, clientY: number): boolean => {
    const sidebar = sidebarRef.current;
    if (!sidebar) return true;

    const rect = sidebar.getBoundingClientRect();
    
    // 🎯 关键修正：检测项目栏实际内容宽度，与容器宽度保持一致
    // 项目栏实际内容宽度：198px（与欢迎界面一致）
    const ACTUAL_SIDEBAR_WIDTH = 198;
    const actualSidebarRight = rect.left + ACTUAL_SIDEBAR_WIDTH;
    
    // 🔍 精确边界检测：鼠标超出项目栏实际内容右边界
    const isOutsideActualSidebar = clientX > actualSidebarRight;
    const isOutsideVertical = clientY < rect.top || clientY > rect.bottom;
    
    // 🎯 最终判断：鼠标离开项目栏实际内容区域
    const isCompletelyOutside = isOutsideActualSidebar || isOutsideVertical;
    
    if (process.env.NODE_ENV === 'development' && isCompletelyOutside) {
      console.log('🎯 鼠标离开项目栏实际内容区域', {
        mouse: { x: clientX, y: clientY },
        actualSidebarRight,
        containerRight: rect.right,
        isOutsideActualSidebar,
        isOutsideVertical
      });
    }
    
    return isCompletelyOutside;
  }, []);

  // 🚀 防抖的智能关闭检测函数 - 超快响应
  const scheduleSmartClose = useCallback(() => {
    if (sidebarTimeoutRef.current) {
      clearTimeout(sidebarTimeoutRef.current);
      sidebarTimeoutRef.current = null;
    }
    
    sidebarTimeoutRef.current = setTimeout(() => {
      // 🎯 三重检查确保精准关闭
      if (lastMousePositionRef.current) {
        const { x, y } = lastMousePositionRef.current;
        
        // 检查1: 鼠标是否完全在侧边栏外
        const isOutside = isMouseCompletelyOutsideSidebar(x, y);
        
        // 检查2: 确保不是在触发区域内
        const isInTriggerZone = isInSmartTriggerZone(x, y);
        
        // 检查3: 最终判断 - 必须在外面且不在触发区域
        if (isOutside && !isInTriggerZone) {
          setIsSidebarHovered(false);
          setIsHoverTriggered(false);
          mouseTrackingRef.current = false;
          
          if (process.env.NODE_ENV === 'development') {
            console.log('✅ 项目栏智能关闭：鼠标完全离开右边框');
          }
        }
      }
    }, 50); // 优化为50ms，超快响应
  }, [isMouseCompletelyOutsideSidebar, isInSmartTriggerZone]);

  // 🎯 高效的全局鼠标移动监听 - 高频检测 + 精准关闭
  const handleGlobalMouseMove = useCallback((e: MouseEvent) => {
    if (!mouseTrackingRef.current) return;
    
    // 🔄 立即更新鼠标位置缓存
    lastMousePositionRef.current = { x: e.clientX, y: e.clientY };
    
    // 🔥 超快预检测：匹配项目栏实际内容宽度
    const ACTUAL_SIDEBAR_WIDTH = 198; // 使用实际宽度
    if (e.clientX <= ACTUAL_SIDEBAR_WIDTH) {
      if (sidebarTimeoutRef.current) {
        clearTimeout(sidebarTimeoutRef.current);
        sidebarTimeoutRef.current = null;
      }
      return;
    }
    
    // 🎯 实时边界检测：鼠标离开项目栏实际内容右边框时立即触发关闭
    if (isSidebarHovered) {
      const isOutside = isMouseCompletelyOutsideSidebar(e.clientX, e.clientY);
      if (isOutside) {
        // 🚀 使用 requestAnimationFrame 确保平滑关闭
        requestAnimationFrame(() => {
          scheduleSmartClose();
        });
      }
    }
  }, [isSidebarHovered, isMouseCompletelyOutsideSidebar, scheduleSmartClose]);

  // 🎯 智能触发区域进入处理
  const handleSmartTriggerZoneEnter = useCallback((e: React.MouseEvent) => {
    // 🔍 智能判断：只有在有效触发区域内才激活
    if (!isInSmartTriggerZone(e.clientX, e.clientY)) {
      return; // 在底部50%区域，不触发
    }
    
    if (sidebarTimeoutRef.current) {
      clearTimeout(sidebarTimeoutRef.current);
      sidebarTimeoutRef.current = null;
    }
    
    mouseTrackingRef.current = true;
    setIsHoverTriggered(true);
    setIsSidebarHovered(true);
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🎯 智能触发区域进入：显示项目栏', {
        mouse: { x: e.clientX, y: e.clientY },
        viewportHeight: window.innerHeight,
        triggerZone: '2px宽度触发区域',
        activeArea: 'top 50% area'
      });
    }
  }, [isInSmartTriggerZone]);

  // 🎯 触发区域全局鼠标移动监听（用于边界检测）
  const handleTriggerZoneMouseMove = useCallback((e: React.MouseEvent) => {
    // 🎯 实时检查是否在有效触发区域
    if (!isInSmartTriggerZone(e.clientX, e.clientY)) {
      // 移出有效触发区域，准备关闭
      lastMousePositionRef.current = { x: e.clientX, y: e.clientY };
      if (!isSidebarHovered) {
        setIsHoverTriggered(false);
        mouseTrackingRef.current = false;
      }
    }
  }, [isInSmartTriggerZone, isSidebarHovered]);

  // 🎯 触发区域离开处理
  const handleTriggerZoneLeave = useCallback(() => {
    // 🎯 离开触发区域时，如果没有进入侧边栏，则准备关闭
    if (!isSidebarHovered) {
      setIsHoverTriggered(false);
      mouseTrackingRef.current = false;
      
      if (process.env.NODE_ENV === 'development') {
        console.log('🎯 离开触发区域：准备关闭项目栏');
      }
    }
  }, [isSidebarHovered]);

  // 🎯 侧边栏区域进入
  const handleSidebarEnter = useCallback(() => {
    if (sidebarTimeoutRef.current) {
      clearTimeout(sidebarTimeoutRef.current);
      sidebarTimeoutRef.current = null;
    }
    setIsSidebarHovered(true);
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🎯 进入侧边栏区域：保持显示');
    }
  }, []);

  // 🎯 侧边栏区域离开 - 即时响应
  const handleSidebarLeave = useCallback((e: React.MouseEvent) => {
    // 🔄 立即更新鼠标位置
    lastMousePositionRef.current = { x: e.clientX, y: e.clientY };
    
    // 🎯 即时检测：如果鼠标确实离开了右边框，立即关闭
    const isOutside = isMouseCompletelyOutsideSidebar(e.clientX, e.clientY);
    if (isOutside) {
      // 🚀 立即关闭，无延迟
      setIsSidebarHovered(false);
      setIsHoverTriggered(false);
      mouseTrackingRef.current = false;
      
      // 清理任何待处理的关闭操作
      if (sidebarTimeoutRef.current) {
        clearTimeout(sidebarTimeoutRef.current);
        sidebarTimeoutRef.current = null;
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('⚡ 侧边栏即时关闭：鼠标离开右边框', {
          mouse: { x: e.clientX, y: e.clientY }
        });
      }
    } else {
      // 🎯 备用延迟关闭（鼠标可能还在边界附近）
      scheduleSmartClose();
      
      if (process.env.NODE_ENV === 'development') {
        console.log('🎯 侧边栏延迟关闭：边界检测');
      }
    }
  }, [isMouseCompletelyOutsideSidebar, scheduleSmartClose]);

  // 🔧 清理函数优化
  const clearTimeouts = useCallback(() => {
    if (sidebarTimeoutRef.current) {
      clearTimeout(sidebarTimeoutRef.current);
      sidebarTimeoutRef.current = null;
    }
  }, []);

  // 🔧 高效的全局监听器管理
  useEffect(() => {
    if (mouseTrackingRef.current) {
      // 使用 passive 监听器提升性能
      document.addEventListener('mousemove', handleGlobalMouseMove, { passive: true });
      
      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
      };
    }
  }, [handleGlobalMouseMove, isSidebarHovered]);

  // 🧹 清理所有资源
  useEffect(() => {
    return () => {
      clearTimeouts();
      mouseTrackingRef.current = false;
      lastMousePositionRef.current = null;
    };
  }, [clearTimeouts]);

  // 布局状态
  const [currentTab, setCurrentTab] = useState<"chat" | "preview">("chat");
  const [device, setDevice] = useState<string>("desktop");
  const [isLargeScreen, setIsLargeScreen] = useState(true);
  const [isClient, setIsClient] = useState(false);
  
  // 引用
  const preview = useRef<HTMLDivElement | null>(null);
  const iframeRef = useRef<HTMLIFrameElement | null>(null);

  // 🔧 删除重复的项目ID管理，由ProjectManager统一管理

  // 项目选择处理
  const handleProjectSelect = useCallback((selectedProject: ProjectItem) => {
    console.log('🔄 切换到项目:', selectedProject.id);
    setIsProjectSwitching(true);
    
    // 关闭侧边栏
    setIsSidebarHovered(false);
    setIsHoverTriggered(false);
    
    // 跳转到选中的项目
    router.push(`/projects/${selectedProject.id}`);
    
    // 清除切换状态
    setTimeout(() => {
      setIsProjectSwitching(false);
    }, 1000);
  }, [router]);

  const handleNewProject = useCallback(() => {
    console.log('🆕 创建新项目');
    
    // 关闭侧边栏
    setIsSidebarHovered(false);
    setIsHoverTriggered(false);
    
    // 跳转到新项目页面
    router.push('/projects/new');
  }, [router]);

  // 🎯 社区项目处理函数
  interface CommunityProject {
    id: number;
    originalProjectId: number;
    userId: number;
    title: string;
    htmlContent: string;
    createdAt: string;
    updatedAt: string;
    author: {
      name: string;
      email: string;
    };
  }

  const handleOpenCommunityProject = useCallback(async (project: CommunityProject) => {
    // 🔐 检查用户登录状态
    if (!user) {
      console.log('⚠️ EditorLayout: 用户未登录，显示登录弹窗');
      // 触发登录弹窗
      window.dispatchEvent(new CustomEvent('show-login-modal'));
      return;
    }

    try {
      // 🎯 使用专门的社区项目创建方法
      const projectManager = ProjectManager.getInstance();
      const projectIdStr = await projectManager.createProjectFromCommunityProject(project.title, project.htmlContent);

      if (projectIdStr) {
        // 🎯 跳转到新创建的项目
        window.location.href = `/projects/${projectIdStr}`;
        toast.success(`已导入社区项目: ${project.title}`);
      }
    } catch (error) {
      console.error('打开社区项目失败:', error);
      toast.error('打开项目失败，请重试');
    }
  }, [user]);

  const handlePreviewProject = useCallback((project: CommunityProject) => {
    // 与欢迎页面相同的逻辑
    console.log('预览社区项目:', project.title);
  }, []);

  const handleOpenCommunityPage = useCallback(() => {
    // 打开社区页面
    setIsCommunityPageMode(true);
    // 关闭侧边栏
    setIsSidebarHovered(false);
    setIsHoverTriggered(false);
  }, []);

  // 🚀 监听全局AI状态
  useEffect(() => {
    console.log('🔧 EditorLayout: 设置全局状态监听器');
    
    const unsubscribe = globalAIState.addListener((state) => {
      console.log('🎯 EditorLayout: 接收到全局状态更新', {
        isGenerating: state.isGenerating,
        projectId: state.currentProjectId,
        contentLength: state.accumulatedContent.length,
        globalChatHistoryLength: state.chatHistory.length,
        currentChatHistoryLength: chatHistoryRef.current.length,
        timestamp: new Date().toISOString()
      });
      
      // 🔧 使用ref来跟踪上一次的状态，避免闭包问题
      const prevState = prevGlobalStateRef.current;
      
      // 🔧 关键修复：只在状态真正改变时更新
      if (!prevState || 
          prevState.isGenerating !== state.isGenerating ||
          prevState.accumulatedContent !== state.accumulatedContent ||
          prevState.chatHistory.length !== state.chatHistory.length) {
        
        console.log('🔄 EditorLayout: 状态确实发生变化，开始处理', {
          stateChanges: {
            isGenerating: prevState ? prevState.isGenerating !== state.isGenerating : 'first_time',
            contentLength: prevState ? prevState.accumulatedContent.length !== state.accumulatedContent.length : 'first_time',
            chatHistoryLength: prevState ? prevState.chatHistory.length !== state.chatHistory.length : 'first_time'
          },
          prevState: prevState ? {
            isGenerating: prevState.isGenerating,
            contentLength: prevState.accumulatedContent.length,
            chatHistoryLength: prevState.chatHistory.length
          } : null,
          newState: {
            isGenerating: state.isGenerating,
            contentLength: state.accumulatedContent.length,
            chatHistoryLength: state.chatHistory.length
          }
        });
        
        prevGlobalStateRef.current = state;
        setGlobalState(state);
        
        // 🔧 关键修复：防止无限循环 - 只在必要时更新chatHistory
        const currentChatHistory = chatHistoryRef.current;
        
        // 🔧 更精确的状态比较：避免不必要的更新
        const shouldUpdateChatHistory = state.chatHistory.length > 0 && (
          // 长度不同
          state.chatHistory.length !== currentChatHistory.length ||
          // 或者最后一条消息的关键属性不同
          (state.chatHistory.length > 0 && currentChatHistory.length > 0 && 
           (() => {
             const lastGlobalMsg = state.chatHistory[state.chatHistory.length - 1];
             const lastCurrentMsg = currentChatHistory[currentChatHistory.length - 1];
             return lastGlobalMsg.id !== lastCurrentMsg.id ||
                    lastGlobalMsg.isGenerating !== lastCurrentMsg.isGenerating ||
                    (lastGlobalMsg.isGenerating && lastGlobalMsg.content !== lastCurrentMsg.content);
           })())
        );
        
        // 🔧 关键修复：添加详细的状态比较调试信息
        console.log('🔍 EditorLayout: 详细状态比较分析', {
          globalChatLength: state.chatHistory.length,
          currentChatLength: currentChatHistory.length,
          lengthDiff: state.chatHistory.length !== currentChatHistory.length,
          globalLastMessage: state.chatHistory.length > 0 ? {
            id: state.chatHistory[state.chatHistory.length - 1].id,
            type: state.chatHistory[state.chatHistory.length - 1].type,
            isGenerating: state.chatHistory[state.chatHistory.length - 1].isGenerating,
            hasContent: !!state.chatHistory[state.chatHistory.length - 1].content,
            hasHtmlContent: !!state.chatHistory[state.chatHistory.length - 1].htmlContent,
            contentPreview: state.chatHistory[state.chatHistory.length - 1].content?.substring(0, 30) || null
          } : null,
          currentLastMessage: currentChatHistory.length > 0 ? {
            id: currentChatHistory[currentChatHistory.length - 1].id,
            type: currentChatHistory[currentChatHistory.length - 1].type,
            isGenerating: currentChatHistory[currentChatHistory.length - 1].isGenerating,
            hasContent: !!currentChatHistory[currentChatHistory.length - 1].content,
            hasHtmlContent: !!currentChatHistory[currentChatHistory.length - 1].htmlContent,
            contentPreview: currentChatHistory[currentChatHistory.length - 1].content?.substring(0, 30) || null
          } : null,
          shouldUpdateChatHistory,
          comparisonDetails: state.chatHistory.length > 0 && currentChatHistory.length > 0 ? {
            idMatch: state.chatHistory[state.chatHistory.length - 1].id === currentChatHistory[currentChatHistory.length - 1].id,
            isGeneratingMatch: state.chatHistory[state.chatHistory.length - 1].isGenerating === currentChatHistory[currentChatHistory.length - 1].isGenerating,
            contentMatch: state.chatHistory[state.chatHistory.length - 1].content === currentChatHistory[currentChatHistory.length - 1].content
          } : null
        });
        
        // 🔧 关键修复：直接以文本形式输出详细比较信息
        console.log('📊 EditorLayout: 状态比较详情（文本格式）');
        console.log('  全局聊天历史长度:', state.chatHistory.length);
        console.log('  本地聊天历史长度:', currentChatHistory.length);
        console.log('  长度是否不同:', state.chatHistory.length !== currentChatHistory.length);
        console.log('  是否应该更新聊天历史:', shouldUpdateChatHistory);
        
        if (state.chatHistory.length > 0) {
          const globalLast = state.chatHistory[state.chatHistory.length - 1];
          console.log('  全局最后消息:', JSON.stringify({
            id: globalLast.id,
            type: globalLast.type,
            isGenerating: globalLast.isGenerating,
            hasContent: !!globalLast.content,
            hasHtmlContent: !!globalLast.htmlContent
          }, null, 2));
        }
        
        if (currentChatHistory.length > 0) {
          const currentLast = currentChatHistory[currentChatHistory.length - 1];
          console.log('  本地最后消息:', JSON.stringify({
            id: currentLast.id,
            type: currentLast.type,
            isGenerating: currentLast.isGenerating,
            hasContent: !!currentLast.content,
            hasHtmlContent: !!currentLast.htmlContent
          }, null, 2));
        }
        
        if (state.chatHistory.length > 0 && currentChatHistory.length > 0) {
          const globalLast = state.chatHistory[state.chatHistory.length - 1];
          const currentLast = currentChatHistory[currentChatHistory.length - 1];
          console.log('  详细比较结果:');
          console.log('    ID匹配:', globalLast.id === currentLast.id);
          console.log('    isGenerating匹配:', globalLast.isGenerating === currentLast.isGenerating);
          console.log('    内容匹配:', globalLast.content === currentLast.content);
          console.log('    全局isGenerating:', globalLast.isGenerating);
          console.log('    本地isGenerating:', currentLast.isGenerating);
        }
        
        // 🔧 关键修复：特别处理AI生成完成的情况
        const hasAIGenerationCompleted = state.chatHistory.length > 0 && 
          currentChatHistory.length > 0 &&
          state.chatHistory.length === currentChatHistory.length &&
          (() => {
            const lastGlobalMsg = state.chatHistory[state.chatHistory.length - 1];
            const lastCurrentMsg = currentChatHistory[currentChatHistory.length - 1];
            // 如果全局状态中AI消息已完成，但本地状态中还在生成，强制更新
            return lastGlobalMsg.type === 'ai' && 
                   lastCurrentMsg.type === 'ai' &&
                   lastGlobalMsg.id === lastCurrentMsg.id &&
                   !lastGlobalMsg.isGenerating && 
                   lastCurrentMsg.isGenerating;
          })();
        
        console.log('🔧 EditorLayout: AI生成完成检查:', hasAIGenerationCompleted);
        
        // 🔧 关键修复：更新shouldUpdateChatHistory的判断逻辑
        const finalShouldUpdate = shouldUpdateChatHistory || hasAIGenerationCompleted;
        console.log('🔧 EditorLayout: 最终是否应该更新:', finalShouldUpdate);
        
        if (finalShouldUpdate) {
          console.log('🔄 EditorLayout: 需要更新聊天历史', {
            reason: state.chatHistory.length !== currentChatHistory.length ? 'length_diff' : 'content_diff',
            globalChatLength: state.chatHistory.length,
            currentChatLength: currentChatHistory.length,
            globalLastMessage: state.chatHistory.length > 0 ? {
              id: state.chatHistory[state.chatHistory.length - 1].id,
              type: state.chatHistory[state.chatHistory.length - 1].type,
              isGenerating: state.chatHistory[state.chatHistory.length - 1].isGenerating,
              hasContent: !!state.chatHistory[state.chatHistory.length - 1].content,
              hasHtmlContent: !!state.chatHistory[state.chatHistory.length - 1].htmlContent
            } : null,
            currentLastMessage: currentChatHistory.length > 0 ? {
              id: currentChatHistory[currentChatHistory.length - 1].id,
              type: currentChatHistory[currentChatHistory.length - 1].type,
              isGenerating: currentChatHistory[currentChatHistory.length - 1].isGenerating,
              hasContent: !!currentChatHistory[currentChatHistory.length - 1].content,
              hasHtmlContent: !!currentChatHistory[currentChatHistory.length - 1].htmlContent
            } : null
          });
          
          // 🔧 关键修复：直接同步更新，确保AI完成状态立即显示
          console.log('🔄 EditorLayout: 开始更新聊天历史到本地状态');
          chatHistoryHookRef.current.setChatHistory([...state.chatHistory]);
          console.log('✅ EditorLayout: 聊天历史已立即更新', {
            newChatLength: state.chatHistory.length,
            lastMessageType: state.chatHistory[state.chatHistory.length - 1]?.type,
            lastMessageGenerating: state.chatHistory[state.chatHistory.length - 1]?.isGenerating,
            lastMessageHasHtml: !!state.chatHistory[state.chatHistory.length - 1]?.htmlContent
          });
          
          // 🔧 关键修复：立即检查本地聊天历史是否已更新
          setTimeout(() => {
            const updatedLocalChatHistory = chatHistoryHookRef.current.chatHistory;
            console.log('🔍 EditorLayout: 检查本地聊天历史更新结果', {
              localChatLength: updatedLocalChatHistory.length,
              lastLocalMessage: updatedLocalChatHistory.length > 0 ? {
                id: updatedLocalChatHistory[updatedLocalChatHistory.length - 1].id,
                type: updatedLocalChatHistory[updatedLocalChatHistory.length - 1].type,
                isGenerating: updatedLocalChatHistory[updatedLocalChatHistory.length - 1].isGenerating,
                hasHtmlContent: !!updatedLocalChatHistory[updatedLocalChatHistory.length - 1].htmlContent
              } : null,
              matchesGlobalState: updatedLocalChatHistory.length === state.chatHistory.length &&
                (updatedLocalChatHistory.length === 0 || 
                 updatedLocalChatHistory[updatedLocalChatHistory.length - 1].isGenerating === 
                 state.chatHistory[state.chatHistory.length - 1].isGenerating)
            });
          }, 100);
        } else {
          console.log('⚪ EditorLayout: 聊天历史无需更新', {
            globalChatLength: state.chatHistory.length,
            currentChatLength: currentChatHistory.length,
            shouldUpdateChatHistory,
            hasAIGenerationCompleted,
            reason: 'no_significant_changes'
          });
        }
        
        // 🎯 只在状态真正变化时更新
        if (state.isGenerating && (!prevState || !prevState.isGenerating)) {
          console.log('🚀 EditorLayout: 开始继承AI生成状态');
        }
        
        // 🎯 实时更新流式内容（但避免重复设置相同内容）
        if (state.isGenerating && state.accumulatedContent && 
            state.accumulatedContent !== (prevState?.accumulatedContent || '')) {
          
          // 🔧 关键修复：使用包装函数避免重复设置相同内容导致iframe循环重载
          if (streamingHtmlRef.current !== state.accumulatedContent) {
            streamingHtmlRef.current = state.accumulatedContent;
            setStreamingHtml(state.accumulatedContent);
          }
          
          // 🔧 关键修复：避免重复更新聊天历史导致循环
          // 只在内容真正变化时更新
          const lastAIMessage = chatHistoryRef.current[chatHistoryRef.current.length - 1];
          if (lastAIMessage && lastAIMessage.type === 'ai' && 
              lastAIMessage.isGenerating && lastAIMessage.content !== state.accumulatedContent) {
            // 使用ref来访问chatHistoryHook，避免闭包问题
            chatHistoryHookRef.current.updateLastAIMessage(state.accumulatedContent);
          }
        }
        
        // 🎯 关键修复：生成完成时立即更新最终内容并确保AI消息状态正确
        if (!state.isGenerating && prevState?.isGenerating && state.accumulatedContent) {
          console.log('✅ EditorLayout: AI生成完成，立即更新最终内容并结束AI消息状态');
          setHtml(state.accumulatedContent);
          updateStreamingHtml('');
          
          // 🎯 关键修复：确保AI消息状态为完成（防止卡在生成状态）
          setTimeout(() => {
            const lastAIMessage = chatHistoryRef.current[chatHistoryRef.current.length - 1];
            if (lastAIMessage && lastAIMessage.type === 'ai' && lastAIMessage.isGenerating) {
              console.log('🎯 EditorLayout: 强制结束AI消息生成状态');
              chatHistoryHookRef.current.completeAIGeneration(state.accumulatedContent);
            }
          }, 100); // 延迟100ms确保chatHistory已经设置
          
          console.log('✅ EditorLayout: AI生成完成，全局状态已更新聊天历史');
        }
      }
    });
    
    return unsubscribe;
  }, []); // 🔧 关键修复：移除依赖数组，防止无限循环

  // 初始化
  useMount(() => {
    setIsClient(true);
    setIsLargeScreen(window.innerWidth >= 1024);
    
    // 🧹 初始化样式污染检测器
    import('../style-panel/utils/StylePollutionDetector').then(({ StylePollutionDetector }) => {
      const detector = StylePollutionDetector.getInstance();
      detector.startMonitoring();
      console.log('🔍 样式污染检测器已启动');
      
      // 添加清理回调
      detector.addCleanupCallback((cleanedHtml) => {
        console.log('🧹 检测到样式污染，已自动清理', { 
          htmlLength: cleanedHtml.length,
          timestamp: new Date().toISOString()
        });
      });
    });
    
    // 🔧 关键修复：初始化时检查全局状态，立即继承聊天历史并确保AI消息状态正确
    const currentGlobalState = globalAIState.getState();
    console.log('🔧 EditorLayout: 初始化时检查全局状态', {
      globalChatLength: currentGlobalState.chatHistory.length,
      currentChatLength: chatHistoryHook.chatHistory.length,
      isGenerating: currentGlobalState.isGenerating,
      projectId: currentGlobalState.currentProjectId,
      contentLength: currentGlobalState.accumulatedContent.length
    });
    
    if (currentGlobalState.chatHistory.length > 0) {
      console.log('🔧 EditorLayout: 初始化时发现全局聊天历史，立即继承', {
        globalChatLength: currentGlobalState.chatHistory.length,
        currentChatLength: chatHistoryHook.chatHistory.length,
        chatMessages: currentGlobalState.chatHistory.map(msg => ({
          type: msg.type,
          isGenerating: msg.isGenerating,
          hasContent: !!msg.content,
          hasHtml: !!msg.htmlContent
        }))
      });
      chatHistoryHookRef.current.setChatHistory([...currentGlobalState.chatHistory]);
      
      // 🔧 关键修复：如果AI已经生成完成，立即设置HTML内容并确保AI消息状态正确
      if (!currentGlobalState.isGenerating && currentGlobalState.accumulatedContent) {
        console.log('🔧 EditorLayout: 检测到AI已生成完成，立即设置最终内容并结束AI消息状态');
        setHtml(currentGlobalState.accumulatedContent);
        
        // 🎯 关键修复：确保AI消息状态为完成（防止卡在生成状态）
        setTimeout(() => {
          const lastAIMessage = chatHistoryRef.current[chatHistoryRef.current.length - 1];
          if (lastAIMessage && lastAIMessage.type === 'ai' && lastAIMessage.isGenerating) {
            console.log('🎯 EditorLayout: 强制结束AI消息生成状态');
            chatHistoryHookRef.current.completeAIGeneration(currentGlobalState.accumulatedContent);
          }
        }, 100);
      }
    }
    
    // 🔧 新增：强制检查AI消息完成状态的逻辑
    setTimeout(() => {
      const globalState = globalAIState.getState();
      const localChatHistory = chatHistoryHookRef.current.chatHistory;
      
      console.log('🔍 EditorLayout: 强制检查AI消息完成状态', {
        globalIsGenerating: globalState.isGenerating,
        globalHasContent: !!globalState.accumulatedContent,
        localChatLength: localChatHistory.length,
        localLastMessage: localChatHistory.length > 0 ? {
          type: localChatHistory[localChatHistory.length - 1].type,
          isGenerating: localChatHistory[localChatHistory.length - 1].isGenerating,
          hasHtml: !!localChatHistory[localChatHistory.length - 1].htmlContent
        } : null
      });
      
      // 🎯 关键修复：如果全局状态已完成但本地AI消息仍在生成，强制完成
      if (!globalState.isGenerating && 
          globalState.accumulatedContent && 
          localChatHistory.length > 0) {
        
        const lastAIMessage = localChatHistory[localChatHistory.length - 1];
        if (lastAIMessage && lastAIMessage.type === 'ai' && lastAIMessage.isGenerating) {
          console.log('🎯 EditorLayout: 发现AI消息卡在生成状态，强制完成', {
            messageId: lastAIMessage.id,
            globalContent: globalState.accumulatedContent.length,
            messageCurrentContent: lastAIMessage.content?.length || 0
          });
          
          // 强制完成AI消息生成
          chatHistoryHookRef.current.completeAIGeneration(globalState.accumulatedContent);
          
          // 同时确保HTML内容正确设置
          if (globalState.accumulatedContent !== html) {
            console.log('🔧 EditorLayout: 同步HTML内容');
            setHtml(globalState.accumulatedContent);
          }
        }
      }
    }, 300); // 延迟300ms确保所有状态都已初始化
    
    // 项目相关初始化
    if (project) {
      console.log('🔧 EditorLayout: 项目已加载，设置编辑模式');
      setLayoutMode('editing');
      
      // 🔧 延迟加载聊天历史，避免与全局状态冲突
      setTimeout(async () => {
        const currentGlobalState = globalAIState.getState();
        
        // 如果全局状态有聊天历史（说明是从WelcomeLayout跳转过来的），则不加载数据库
        if (currentGlobalState.chatHistory.length > 0) {
          console.log('🔧 EditorLayout: 检测到全局状态有聊天历史，跳过数据库加载', {
            globalChatLength: currentGlobalState.chatHistory.length,
            isGenerating: currentGlobalState.isGenerating
          });
          return;
        }
        
        // 只有在全局状态没有聊天历史时，才从数据库加载（直接访问项目页面的情况）
        console.log('🔧 EditorLayout: 全局状态无聊天历史，从数据库加载', { 
          projectId: project.id,
          projectManagerCurrentId: projectManager.currentProjectId
        });
        try {
          await chatHistoryHook.loadChatHistory(project.id);
          console.log('✅ EditorLayout: 聊天历史加载完成', {
            chatHistoryLength: chatHistoryHook.chatHistory.length
          });
        } catch (error) {
          console.error('❌ EditorLayout: 聊天历史加载失败', error);
        }
      }, 100);
      
      // 🔧 关键修复：首先设置ProjectManager的currentProjectId
      ProjectManager.getInstance().currentProjectId = project.id.toString();
      console.log('🔧 EditorLayout: 初始化时设置ProjectManager.currentProjectId', {
        projectId: project.id,
        currentProjectId: ProjectManager.getInstance().currentProjectId
      });
      
      // 🔧 关键修复：设置项目的HTML内容
      if (project.html_content) {
        console.log('🔧 EditorLayout: 设置项目HTML内容', {
          projectId: project.id,
          htmlLength: project.html_content.length
        });
        setHtml(project.html_content);
      }
      
      // 🔧 设置项目的提示词列表
      if (project.prompts && project.prompts.length > 0) {
        console.log('🔧 EditorLayout: 设置项目提示词', project.prompts);
        setPrompts(project.prompts);
      }
      
      // 🔧 修复：确保项目ID正确设置后再加载聊天历史
      console.log('🔧 EditorLayout: 开始设置项目状态和加载聊天历史', {
        projectId: project.id,
        currentChatHistoryLength: chatHistoryHook.chatHistory.length,
        projectManagerCurrentId: ProjectManager.getInstance().currentProjectId
      });
      
      // 🔧 关键修复：确保ProjectManager的currentProjectId与当前项目一致
      const projectManager = ProjectManager.getInstance();
      if (projectManager.currentProjectId !== project.id.toString()) {
        console.log('🔧 EditorLayout: 同步ProjectManager的currentProjectId', {
          old: projectManager.currentProjectId,
          new: project.id.toString()
        });
        projectManager.currentProjectId = project.id.toString();
      }
      
      // 获取当前活跃版本号
      const fetchActiveVersion = async () => {
        try {
          const response = await fetch(`/api/projects/${project.id}/versions`);
          if (response.ok) {
            const data = await response.json();
            const activeVersion = data.versions?.find((v: { is_active: boolean; version_number: number }) => v.is_active);
            if (activeVersion) {
              setCurrentVersionNumber(activeVersion.version_number);
            }
          }
        } catch (error) {
          console.error('Failed to fetch active version:', error);
        }
      };
      
      fetchActiveVersion();
    } else if (htmlStorage) {
      setLayoutMode('generation');
    } else {
      setLayoutMode('welcome');
    }
  });

  // 事件处理函数
  const handleBackToWelcome = () => {
    if (project?.id) {
      router.push('/projects/new');
      return;
    }
    
    removeHtmlStorage();
    setLayoutMode('welcome');
    setHtml(defaultHTML);
    setHtmlHistory([]);
    setPrompts([]);
    chatHistoryHook.clearChatHistory();
  };

  const handleSwitchToEditor = (htmlContent?: string) => {
    setLayoutMode('editing');
    // 🎯 如果传入了HTML内容，则更新预览区域
    if (htmlContent) {
      setHtml(htmlContent);
      console.log('🎯 EditorLayout: 切换到编辑器并更新HTML内容', {
        htmlLength: htmlContent.length
      });
    }
  };

  // 🚀 实时过渡：处理开始创建（立即开始后台工作）
  const handleStartCreating = useCallback(async (prompt: string, chatHistory?: ChatMessage[], html?: string) => {
    console.log('🎯 EditorLayout: handleStartCreating开始 - 立即启动后台工作', {
      prompt: prompt?.substring(0, 50) + '...',
      chatHistoryLength: chatHistory?.length || 0,
      htmlLength: html?.length || 0,
      projectManagerCurrentId: ProjectManager.getInstance().currentProjectId
    });

    // 🚀 立即切换到编辑模式（不等待任何内容）
    setLayoutMode('editing');
    console.log('🚀 EditorLayout: 立即切换到编辑模式，开始后台工作');

    // 设置编辑器内容（如果有）
    if (html) {
      setHtml(html);
      console.log('✅ EditorLayout: HTML内容已设置', { htmlLength: html.length });
    }
    
    // 设置聊天历史（如果有）
    if (chatHistory && chatHistory.length > 0) {
      chatHistoryHook.setChatHistory(chatHistory);
      console.log('✅ EditorLayout: 聊天历史已设置', { 
        chatHistoryLength: chatHistory.length,
        chatHistoryPreview: chatHistory.map(msg => ({
          type: msg.type,
          contentLength: msg.content?.length || 0,
          hasHtml: !!msg.htmlContent
        }))
      });
    } else {
      console.log('🔄 EditorLayout: 没有聊天历史传入，从服务器加载');
      // 🚀 立即加载聊天历史（不延迟）
      const projectManager = ProjectManager.getInstance();
      if (projectManager.currentProjectId) {
        try {
          console.log('🔄 EditorLayout: 从服务器加载聊天历史', {
            projectId: projectManager.currentProjectId
          });
          await chatHistoryHook.loadChatHistory(parseInt(projectManager.currentProjectId!));
          console.log('✅ EditorLayout: 聊天历史加载完成', {
            chatHistoryLength: chatHistoryHook.chatHistory.length
          });
        } catch (error) {
          console.error('❌ EditorLayout: 聊天历史加载失败', error);
        }
      }
    }
    
    // 添加到HTML历史
    if (html && html.trim().length > 0) {
      setHtmlHistory(prev => [...prev, {
        html,
        createdAt: new Date(),
        prompt: prompt || ''
      }]);
    }

    // 更新提示词列表
    if (prompt && prompt !== '查看生成的代码' && prompt !== '开始新项目') {
      setPrompts(prev => {
        const newPrompts = prev.includes(prompt) ? prev : [...prev, prompt];
        return newPrompts;
      });
    }
    console.log('✅ EditorLayout: 已切换到编辑模式，全局状态管理已接管AI生成', {
      projectManagerCurrentId: ProjectManager.getInstance().currentProjectId,
      chatHistoryLength: chatHistory?.length || 0,
      globalAIGenerating: globalState?.isGenerating || false
    });
  }, [chatHistoryHook, globalState]);

  // 🔧 智能项目自动保存机制（高性能版）
  const handleAutoSave = useCallback(async (html: string, prompt: string) => {
    const projectManager = ProjectManager.getInstance();
    if (!projectManager.currentProjectId) {
      console.log('⚠️ 没有当前项目，跳过自动保存');
      return;
    }

    try {
      PerformanceLogger.startTimer('auto-save');
      
      // 🎯 防重复保存：检查内容是否有实质性变化
      const lastHistory = htmlHistory[htmlHistory.length - 1];
      if (lastHistory && lastHistory.html === html && lastHistory.prompt === prompt) {
        console.log('⚠️ 内容无变化，跳过保存');
        return;
      }

      // 🔧 修复：使用ProjectManager实例
      const currentProjectId = projectManager.currentProjectId;
      if (!currentProjectId) {
        console.log('⚠️ 项目ID为空，跳过保存');
        return;
      }
      
      const result = await projectManager.saveConversationToProject(
        parseInt(currentProjectId), 
        prompt, 
        html
      );
      
      if (result.success) {
        console.log(`✅ 项目自动保存成功 ${result.versionNumber ? `(v${result.versionNumber})` : ''}`);
        
        // 🎯 更新版本号（如果有）
        if (result.versionNumber) {
          setCurrentVersionNumber(result.versionNumber);
        }
      }
      
      PerformanceLogger.endTimer('auto-save');
    } catch (error) {
      PerformanceLogger.error('❌ 自动保存失败:', error);
    }
  }, [htmlHistory, setCurrentVersionNumber]);

  const handleAISuccess = useCallback((newHtml: string, prompt?: string) => {
    PerformanceLogger.startTimer('ai-success-handler');
    PerformanceLogger.log('🎯 EditorLayout: AI生成成功', { 
      htmlLength: newHtml.length,
      prompt: prompt?.substring(0, 50) + '...' 
    });
    
    setHtml(newHtml);
    updateStreamingHtml(""); // 🎯 清空流式内容
    
    // 添加到历史记录
    setHtmlHistory(prev => [
      ...prev,
      {
        html: newHtml,
        createdAt: new Date(),
        prompt: prompt || '未知提示'
      }
    ]);
    
    // 🔧 智能自动保存：只在有项目时保存
    const projectManager = ProjectManager.getInstance();
    if (projectManager.currentProjectId && prompt) {
      // 🎯 延迟保存，避免阻塞UI
      setTimeout(() => {
        handleAutoSave(newHtml, prompt);
      }, 100);
    }
    
    PerformanceLogger.endTimer('ai-success-handler');
  }, [handleAutoSave]);

  const handleStreamingHtml = useCallback((streamContent: string) => {
    updateStreamingHtml(streamContent);
  }, [updateStreamingHtml]);

  const handleNewPrompt = (prompt: string) => {
    setPrompts(prev => [...prev, prompt]);
  };

  // 所有模式都需要ProjectProvider包装
  return (
    <ProjectProvider>
      {layoutMode === 'welcome' ? (
        <WelcomeLayout
          onStartCreating={handleStartCreating}
        />
      ) : (
        // 编辑模式布局
      <section 
        className="h-screen bg-background flex flex-col overscroll-none"
        style={{
          touchAction: 'pan-x pan-y',
          overscrollBehavior: 'none',
          WebkitOverflowScrolling: 'touch'
        }}
      >
        <Header
          onLogoClick={handleBackToWelcome}
          project={currentProject ? { id: currentProject.id, title: currentProject.title } : undefined}
          htmlContent={html}
          onMobileSidebarToggle={() => {
            // 编辑器模式下的移动端侧边栏切换
            if (typeof window !== 'undefined' && window.innerWidth < 1024) {
              setIsSidebarHovered(!isSidebarHovered);
            }
          }}
        >
          <DeployButton html={html} prompts={prompts} project={project} />
        </Header>

        {/* 社区页面模式 - 在编辑器布局内部渲染，考虑Header高度 */}
        {isCommunityPageMode ? (
          <div className="flex-1 overflow-hidden" style={{ height: 'calc(100vh - 44px)' }}>
            <CommunityPage
              onClose={() => setIsCommunityPageMode(false)}
              onOpenProject={handleOpenCommunityProject}
              onPreviewProject={handlePreviewProject}
              avoidSystemHeader={false}
            />
          </div>
        ) : (
        
        <main className="bg-background flex-1 flex w-full min-h-0 relative transition-all duration-300 overflow-hidden" style={{ margin: 0, padding: 0 }}>
          {/* 🎯 完美相对布局：确保总宽度精确匹配 */}
          <div className="flex w-full h-full relative">
            
            {/* 🎯 左侧智能触发区域 - 2px宽度，与ChatArea完美配合 */}
            <div
              ref={triggerZoneRef}
              className="w-0.5 h-full flex-shrink-0 z-30 cursor-pointer relative"
              onMouseEnter={handleSmartTriggerZoneEnter}
              onMouseLeave={handleTriggerZoneLeave}
              onMouseMove={handleTriggerZoneMouseMove}
              style={{ 
                // 🎯 最小化触发区域宽度，减少视觉干扰
                width: '2px',
                // 调试用渐变（生产环境不显示）
                background: process.env.NODE_ENV === 'development' 
                  ? 'linear-gradient(to bottom, rgba(59,130,246,0.06) 0%, rgba(59,130,246,0.06) 50%, rgba(239,68,68,0.06) 50%, rgba(239,68,68,0.06) 100%)'
                  : 'transparent'
              }}
              title="智能项目栏触发区域（底部50%区域已禁用）"
            />

            {/* 🚀 悬浮项目栏 - 让ProjectSidebar组件自己处理定位，避免双重定位冲突 */}
            {(isHoverTriggered || isSidebarHovered) && (
              <div
                ref={sidebarRef}
                className={`z-50 transition-all duration-300 ease-in-out pointer-events-auto ${
                  isSidebarHovered ? 'opacity-100' : 'opacity-0'
                }`}
                style={{
                  // 🎯 移除重复的定位样式，让ProjectSidebar组件的project-sidebar-professional类来处理定位
                  // 只保留透明度过渡效果
                  pointerEvents: 'auto'
                }}
                onMouseEnter={handleSidebarEnter}
                onMouseLeave={handleSidebarLeave}
              >
                <ProjectSidebar
                  isOpen={isSidebarHovered}
                  onClose={() => {
                    setIsSidebarHovered(false);
                    setIsHoverTriggered(false);
                    
                    if (process.env.NODE_ENV === 'development') {
                      console.log('🎯 ProjectSidebar onClose 调用');
                    }
                  }}
                  currentProjectId={project?.id}
                  onProjectSelect={handleProjectSelect}
                  onNewProject={handleNewProject}
                  onOpenCommunityPage={handleOpenCommunityPage}
                />
              </div>
            )}

            {/* 🎯 内容区域容器 - 使用可拖拽分隔的布局，确保正常交互 */}
            <div className="flex-1 flex min-h-0 overflow-hidden relative" style={{ pointerEvents: 'auto' }}>
              
              {/* 🎯 桌面端：可拖拽分隔布局 */}
              {isClient && isLargeScreen ? (
                <>
                  {/* 聊天区域 - 动态宽度 */}
                  <div style={{ width: `${chatAreaWidth}%` }} className="flex-shrink-0 min-h-0">
                    <ChatArea
                      html={html}
                      setHtml={setHtml}
                      htmlHistory={htmlHistory}
                      setHtmlHistory={setHtmlHistory}
                      isAiWorking={isAiWorking}
                      setIsAiWorking={setIsAiWorking}
                      isEditableModeEnabled={isEditableModeEnabled}
                      setIsEditableModeEnabled={setIsEditableModeEnabled}
                      selectedElement={selectedElement}
                      setSelectedElement={setSelectedElement}
                      chatHistory={chatHistoryHook.chatHistory}
                      setChatHistory={chatHistoryHook.setChatHistory}
                      currentTab={currentTab}
                      setCurrentTab={setCurrentTab}
                      isLargeScreen={isLargeScreen}
                      isClient={isClient}
                      isProjectSwitching={isProjectSwitching}
                      sharedInputPrompt={sharedInputPrompt}
                      setSharedInputPrompt={setSharedInputPrompt}
                      onSwitchToEditor={handleSwitchToEditor}
                      onSuccess={handleAISuccess}
                      onNewPrompt={handleNewPrompt}
                      onStreamingHtml={handleStreamingHtml}
                      streamingHtml={streamingHtml}
                      currentVersionNumber={currentVersionNumber}
                    />
                  </div>

                  {/* 🎯 可拖拽分隔条 */}
                  <ResizableSplitter
                    direction="vertical"
                    initialPosition={chatAreaWidth}
                    minLeftSize={17}
                    maxLeftSize={65}
                    onPositionChange={handleWidthChange}
                  />

                  {/* 预览区域 - 剩余宽度 */}
                  <div className="flex-1 min-h-0 overflow-hidden">
                    <PreviewArea
                      html={html}
                      setHtml={setHtml}
                      streamingHtml={streamingHtml}
                      isAiWorking={isAiWorking}
                      isEditableModeEnabled={isEditableModeEnabled}
                      setIsEditableModeEnabled={setIsEditableModeEnabled}
                      setSelectedElement={setSelectedElement}
                      currentTab={currentTab}
                      setCurrentTab={setCurrentTab}
                      device={device}
                      setDevice={setDevice}
                      isLargeScreen={isLargeScreen}
                      isClient={isClient}
                      project={project}
                      currentVersionNumber={currentVersionNumber}
                      setCurrentVersionNumber={setCurrentVersionNumber}
                      preview={preview}
                      iframeRef={iframeRef}
                    />
                  </div>
                </>
              ) : (
                /* 🎯 移动端：传统标签页布局 */
                <>
                  {currentTab === "chat" && (
                    <ChatArea
                      html={html}
                      setHtml={setHtml}
                      htmlHistory={htmlHistory}
                      setHtmlHistory={setHtmlHistory}
                      isAiWorking={isAiWorking}
                      setIsAiWorking={setIsAiWorking}
                      isEditableModeEnabled={isEditableModeEnabled}
                      setIsEditableModeEnabled={setIsEditableModeEnabled}
                      selectedElement={selectedElement}
                      setSelectedElement={setSelectedElement}
                      chatHistory={chatHistoryHook.chatHistory}
                      setChatHistory={chatHistoryHook.setChatHistory}
                      currentTab={currentTab}
                      setCurrentTab={setCurrentTab}
                      isLargeScreen={isLargeScreen}
                      isClient={isClient}
                      isProjectSwitching={isProjectSwitching}
                      sharedInputPrompt={sharedInputPrompt}
                      setSharedInputPrompt={setSharedInputPrompt}
                      onSwitchToEditor={handleSwitchToEditor}
                      onSuccess={handleAISuccess}
                      onNewPrompt={handleNewPrompt}
                      onStreamingHtml={handleStreamingHtml}
                      streamingHtml={streamingHtml}
                      currentVersionNumber={currentVersionNumber}
                    />
                  )}

                  {currentTab === "preview" && (
                    <PreviewArea
                      html={html}
                      setHtml={setHtml}
                      streamingHtml={streamingHtml}
                      isAiWorking={isAiWorking}
                      isEditableModeEnabled={isEditableModeEnabled}
                      setIsEditableModeEnabled={setIsEditableModeEnabled}
                      setSelectedElement={setSelectedElement}
                      currentTab={currentTab}
                      setCurrentTab={setCurrentTab}
                      device={device}
                      setDevice={setDevice}
                      isLargeScreen={isLargeScreen}
                      isClient={isClient}
                      project={project}
                      currentVersionNumber={currentVersionNumber}
                      setCurrentVersionNumber={setCurrentVersionNumber}
                      preview={preview}
                      iframeRef={iframeRef}
                    />
                  )}
                </>
              )}
              
            </div>
          </div>
        </main>
        )}
        </section>
        )}

        {/* 🔐 登录弹窗 */}
        <AuthModal
          open={showLoginModal}
          onClose={() => setShowLoginModal(false)}
          onSuccess={() => {
            // AuthModal 已经处理了弹窗关闭和用户状态刷新
            toast.success('登录成功！现在可以使用完整功能了');
          }}
          title="登录 LoomRun"
          description="登录后即可导入社区项目、保存项目历史"
        />
      </ProjectProvider>
    );
}; 