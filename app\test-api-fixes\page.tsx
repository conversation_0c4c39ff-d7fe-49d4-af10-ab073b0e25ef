"use client";

import { useState } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Header } from "@/components/editor/header";
import { 
  Database, 
  User, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  History,
  TrendingDown,
  Bug,
  Zap
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface TestResult {
  table: string;
  status: 'success' | 'error';
  count?: number;
  data?: any;
  error?: string;
  message: string;
}

export default function TestAPIFixesPage() {
  const { user } = useUser();
  const router = useRouter();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [historyTest, setHistoryTest] = useState<any>(null);
  const [consumptionTest, setConsumptionTest] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // 测试数据库连接
  const testDatabase = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/points/test-db');
      const data = await response.json();
      if (data.success) {
        setTestResults(data.data.testResults);
        toast.success(`数据库测试完成: ${data.data.summary.successCount}/${data.data.summary.totalTests} 成功`);
      } else {
        toast.error('数据库测试失败');
      }
    } catch (error) {
      console.error('数据库测试失败:', error);
      toast.error('数据库测试失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试历史记录API
  const testHistoryAPI = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/points/history?limit=10');
      const data = await response.json();
      setHistoryTest(data);
      if (data.success) {
        toast.success(`历史记录API测试成功: ${data.data.history.length} 条记录`);
      } else {
        toast.error('历史记录API测试失败');
      }
    } catch (error) {
      console.error('历史记录API测试失败:', error);
      toast.error('历史记录API测试失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试消费记录API
  const testConsumptionAPI = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/points/consumption?limit=10');
      const data = await response.json();
      setConsumptionTest(data);
      if (data.success) {
        toast.success(`消费记录API测试成功: ${data.data.consumptionRecords.length} 条记录`);
      } else {
        toast.error('消费记录API测试失败');
      }
    } catch (error) {
      console.error('消费记录API测试失败:', error);
      toast.error('消费记录API测试失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: 'success' | 'error') => {
    return status === 'success' ? 
      <CheckCircle className="w-4 h-4 text-green-600" /> : 
      <AlertCircle className="w-4 h-4 text-red-600" />;
  };

  const getStatusColor = (status: 'success' | 'error') => {
    return status === 'success' ? 
      'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
      'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
  };

  return (
    <div className="h-screen bg-background flex flex-col">
      <Header onLogoClick={() => router.push('/')} />
      
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          <div className="flex items-center gap-2 mb-6">
            <Bug className="w-6 h-6" />
            <h1 className="text-2xl font-bold">API 修复验证测试</h1>
          </div>

          {/* 用户状态 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                当前用户状态
              </CardTitle>
              <CardDescription>验证API修复后的功能</CardDescription>
            </CardHeader>
            <CardContent>
              {user ? (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{user.id}</div>
                    <div className="text-sm text-muted-foreground">用户ID</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{user.points || 0}</div>
                    <div className="text-sm text-muted-foreground">当前积分</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="text-lg font-bold text-purple-600">{user.total_earned_points || 0}</div>
                    <div className="text-sm text-muted-foreground">累计获得</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <div className="text-lg font-bold text-orange-600">{user.total_spent_points || 0}</div>
                    <div className="text-sm text-muted-foreground">累计消费</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">请先登录以进行API测试</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 修复说明 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-yellow-600" />
                修复的问题
              </CardTitle>
              <CardDescription>本次修复解决的API查询错误</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-red-600">修复前的错误</h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <AlertCircle className="w-4 h-4 text-red-500 mt-0.5" />
                      <span>executeQuery 函数未正确导入</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertCircle className="w-4 h-4 text-red-500 mt-0.5" />
                      <span>SQL 参数数组被意外修改</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertCircle className="w-4 h-4 text-red-500 mt-0.5" />
                      <span>数组解构语法错误</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertCircle className="w-4 h-4 text-red-500 mt-0.5" />
                      <span>消费记录表可能为空的处理</span>
                    </li>
                  </ul>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-600">修复后的改进</h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>正确导入 executeQuery 函数</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>使用扩展运算符避免参数污染</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>修正数组解构和类型定义</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>优雅处理空表和异常情况</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 测试按钮 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Button 
              onClick={testDatabase}
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
              <Database className="w-4 h-4" />
              测试数据库连接
            </Button>
            
            <Button 
              onClick={testHistoryAPI}
              disabled={loading}
              variant="outline"
              className="flex items-center gap-2"
            >
              {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
              <History className="w-4 h-4" />
              测试历史记录API
            </Button>
            
            <Button 
              onClick={testConsumptionAPI}
              disabled={loading}
              variant="outline"
              className="flex items-center gap-2"
            >
              {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
              <TrendingDown className="w-4 h-4" />
              测试消费记录API
            </Button>
          </div>

          {/* 数据库测试结果 */}
          {testResults.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  数据库测试结果
                </CardTitle>
                <CardDescription>各个数据表的连接和查询状态</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {testResults.map((result, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(result.status)}
                        <div>
                          <div className="font-medium">{result.table}</div>
                          <div className="text-sm text-muted-foreground">{result.message}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(result.status)} variant="secondary">
                          {result.status}
                        </Badge>
                        {result.count !== undefined && (
                          <Badge variant="outline">
                            {result.count} 条
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* API测试结果 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {historyTest && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <History className="w-5 h-5" />
                    历史记录API结果
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>状态:</span>
                      <Badge className={historyTest.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {historyTest.success ? '成功' : '失败'}
                      </Badge>
                    </div>
                    {historyTest.success && (
                      <>
                        <div className="flex items-center justify-between">
                          <span>记录数:</span>
                          <span>{historyTest.data.history.length}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>总数:</span>
                          <span>{historyTest.data.pagination.total}</span>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {consumptionTest && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingDown className="w-5 h-5" />
                    消费记录API结果
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>状态:</span>
                      <Badge className={consumptionTest.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {consumptionTest.success ? '成功' : '失败'}
                      </Badge>
                    </div>
                    {consumptionTest.success && (
                      <>
                        <div className="flex items-center justify-between">
                          <span>记录数:</span>
                          <span>{consumptionTest.data.consumptionRecords.length}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>统计数:</span>
                          <span>{consumptionTest.data.statistics.length}</span>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
