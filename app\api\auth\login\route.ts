import { NextRequest, NextResponse } from "next/server";
import { loginWithPhone, loginWithWechat } from "@/lib/auth-service";
import { initDatabase } from "@/lib/mysql";

// 初始化数据库
initDatabase().catch(console.error);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, phone, code, wechatCode } = body;

    if (type === "phone") {
      // 手机验证码登录
      if (!phone || !code) {
        return NextResponse.json(
          { success: false, message: "手机号和验证码不能为空" },
          { status: 400 }
        );
      }

      const result = await loginWithPhone(phone, code);
      if (!result) {
        return NextResponse.json(
          { success: false, message: "验证码错误或已过期" },
          { status: 400 }
        );
      }

      // 设置JWT Token到Cookie
      const response = NextResponse.json(
        { 
          success: true, 
          user: result.user,
          message: "登录成功"
        },
        { status: 200 }
      );

      response.cookies.set("loomrun_token", result.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
        path: "/"
      });

      return response;
    } 
    
    else if (type === "wechat") {
      // 微信登录
      if (!wechatCode) {
        return NextResponse.json(
          { success: false, message: "微信授权码不能为空" },
          { status: 400 }
        );
      }

      const result = await loginWithWechat(wechatCode);
      if (!result) {
        return NextResponse.json(
          { success: false, message: "微信登录失败" },
          { status: 400 }
        );
      }

      // 设置JWT Token到Cookie
      const response = NextResponse.json(
        { 
          success: true, 
          user: result.user,
          message: "登录成功"
        },
        { status: 200 }
      );

      response.cookies.set("loomrun_token", result.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
        path: "/"
      });

      return response;
    }
    
    else {
      return NextResponse.json(
        { success: false, message: "不支持的登录方式" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("登录API错误:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
} 
