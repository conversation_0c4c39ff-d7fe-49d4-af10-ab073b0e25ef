import { TailwindCleaner } from './TailwindCleaner';

// 样式污染检测器 - 实时监控和清理HTML中的重复样式内容
export class StylePollutionDetector {
  private static instance: StylePollutionDetector | null = null;
  private observer: MutationObserver | null = null;
  private isMonitoring = false;
  private cleanupCallbacks: Array<(html: string) => void> = [];

  private constructor() {}

  static getInstance(): StylePollutionDetector {
    if (!this.instance) {
      this.instance = new StylePollutionDetector();
    }
    return this.instance;
  }

  /**
   * 开始监控样式污染
   * @param targetDocument 要监控的文档对象
   */
  startMonitoring(targetDocument: Document = document): void {
    if (this.isMonitoring) {
      console.log('🔍 样式污染检测器已在运行中');
      return;
    }

    console.log('🔍 启动样式污染检测器...');
    this.isMonitoring = true;

    // 创建MutationObserver来监控DOM变化
    this.observer = new MutationObserver((mutations) => {
      let hasStyleChanges = false;

      mutations.forEach((mutation) => {
        // 检测是否有样式相关的变化
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              // 检测新增的script或style标签
              if (element.tagName === 'SCRIPT' || element.tagName === 'STYLE') {
                hasStyleChanges = true;
              }
              // 检测包含Tailwind CDN的script标签
              if (element.tagName === 'SCRIPT' && 
                  element.getAttribute('src')?.includes('tailwindcss.com')) {
                hasStyleChanges = true;
              }
            }
          });
        }
      });

      if (hasStyleChanges) {
        // 延迟检测，避免频繁触发
        setTimeout(() => {
          this.detectAndClean(targetDocument);
        }, 100);
      }
    });

    // 开始观察
    this.observer.observe(targetDocument.documentElement, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });

    // 立即进行一次检测
    this.detectAndClean(targetDocument);
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.isMonitoring = false;
    console.log('🔍 样式污染检测器已停止');
  }

  /**
   * 检测并清理样式污染
   * @param targetDocument 目标文档
   */
  private detectAndClean(targetDocument: Document): void {
    const html = targetDocument.documentElement.outerHTML;
    const duplicates = TailwindCleaner.detectDuplicates(html);

    if (duplicates.hasDuplicateCDN || duplicates.hasDuplicateStyles) {
      console.warn('⚠️ 检测到样式污染:', {
        duplicateCDN: duplicates.cdnCount,
        duplicateStyles: duplicates.styleCount,
        timestamp: new Date().toISOString()
      });

      // 自动清理
      const cleanedHtml = TailwindCleaner.cleanHTML(html);
      
      // 通知清理回调
      this.cleanupCallbacks.forEach(callback => {
        try {
          callback(cleanedHtml);
        } catch (error) {
          console.error('清理回调执行失败:', error);
        }
      });
    }
  }

  /**
   * 添加清理回调函数
   * @param callback 清理完成后的回调函数
   */
  addCleanupCallback(callback: (html: string) => void): void {
    this.cleanupCallbacks.push(callback);
  }

  /**
   * 移除清理回调函数
   * @param callback 要移除的回调函数
   */
  removeCleanupCallback(callback: (html: string) => void): void {
    const index = this.cleanupCallbacks.indexOf(callback);
    if (index > -1) {
      this.cleanupCallbacks.splice(index, 1);
    }
  }

  /**
   * 手动检测HTML中的样式污染
   * @param html HTML内容
   * @returns 检测结果
   */
  static detectPollution(html: string): {
    isPolluted: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const duplicates = TailwindCleaner.detectDuplicates(html);
    const issues: string[] = [];
    const suggestions: string[] = [];

    if (duplicates.hasDuplicateCDN) {
      issues.push(`发现 ${duplicates.cdnCount} 个重复的Tailwind CDN脚本`);
      suggestions.push('移除重复的Tailwind CDN脚本，只保留一个');
    }

    if (duplicates.hasDuplicateStyles) {
      issues.push(`发现 ${duplicates.tailwindStyleCount} 个重复的Tailwind样式块`);
      suggestions.push('清理重复的Tailwind CSS样式块');
    }

    // 🔧 新增：显示样式块分析信息
    if (duplicates.tailwindStyleCount > 0 || duplicates.userStyleCount > 0) {
      console.log('📊 样式块分析结果:', {
        总样式块: duplicates.styleCount,
        Tailwind样式块: duplicates.tailwindStyleCount,
        用户自定义样式块: duplicates.userStyleCount
      });
    }

    // 检测过大的样式块
    const styleBlocks = html.match(/<style[^>]*>[\s\S]*?<\/style>/gi) || [];
    styleBlocks.forEach((block, index) => {
      if (block.length > 50000) { // 50KB以上的样式块
        issues.push(`样式块 #${index + 1} 过大 (${Math.round(block.length / 1024)}KB)`);
        suggestions.push('考虑优化或分割大型样式块');
      }
    });

    return {
      isPolluted: issues.length > 0,
      issues,
      suggestions
    };
  }

  /**
   * 生成样式污染报告
   * @param html HTML内容
   * @returns 详细报告
   */
  static generateReport(html: string): {
    summary: string;
    details: {
      cdnCount: number;
      styleBlockCount: number;
      totalStyleSize: number;
      issues: string[];
      recommendations: string[];
    };
  } {
    const duplicates = TailwindCleaner.detectDuplicates(html);
    const pollution = this.detectPollution(html);
    
    const styleBlocks = html.match(/<style[^>]*>[\s\S]*?<\/style>/gi) || [];
    const totalStyleSize = styleBlocks.reduce((sum, block) => sum + block.length, 0);

    const summary = pollution.isPolluted 
      ? `检测到 ${pollution.issues.length} 个样式污染问题`
      : '未检测到样式污染问题';

    return {
      summary,
      details: {
        cdnCount: duplicates.cdnCount,
        styleBlockCount: styleBlocks.length,
        totalStyleSize,
        issues: pollution.issues,
        recommendations: pollution.suggestions
      }
    };
  }

  /**
   * 获取监控状态
   */
  getStatus(): {
    isMonitoring: boolean;
    callbackCount: number;
  } {
    return {
      isMonitoring: this.isMonitoring,
      callbackCount: this.cleanupCallbacks.length
    };
  }
} 