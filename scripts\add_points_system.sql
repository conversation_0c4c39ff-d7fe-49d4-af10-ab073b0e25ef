ALTER TABLE users ADD COLUMN points int NOT NULL DEFAULT 0;
ALTER TABLE users ADD COLUMN total_earned_points int NOT NULL DEFAULT 0;
ALTER TABLE users ADD COLUMN total_spent_points int NOT NULL DEFAULT 0;

CREATE TABLE system_settings (
  id int NOT NULL AUTO_INCREMENT,
  setting_key varchar(100) NOT NULL,
  setting_value text,
  setting_type enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  description varchar(500),
  category varchar(50) NOT NULL DEFAULT 'general',
  is_active tinyint(1) NOT NULL DEFAULT 1,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY unique_setting_key (setting_key),
  KEY idx_category (category),
  KEY idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE points_transactions (
  id int NOT NULL AUTO_INCREMENT,
  user_id int NOT NULL,
  transaction_type enum('earn','spend') NOT NULL,
  points_amount int NOT NULL,
  balance_before int NOT NULL,
  balance_after int NOT NULL,
  source_type enum('registration','ai_request','export','recharge','subscription','admin_adjust','refund') NOT NULL,
  source_id varchar(100),
  description varchar(500),
  metadata json,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_user_id (user_id),
  KEY idx_transaction_type (transaction_type),
  KEY idx_source_type (source_type),
  KEY idx_created_at (created_at),
  KEY idx_user_created (user_id, created_at DESC),
  CONSTRAINT points_transactions_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE ai_models (
  id int NOT NULL AUTO_INCREMENT,
  model_key varchar(100) NOT NULL,
  model_name varchar(200) NOT NULL,
  points_per_request int NOT NULL DEFAULT 0,
  description varchar(500),
  is_active tinyint(1) NOT NULL DEFAULT 1,
  display_order int NOT NULL DEFAULT 999,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY unique_model_key (model_key),
  KEY idx_active (is_active),
  KEY idx_display_order (display_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE export_types (
  id int NOT NULL AUTO_INCREMENT,
  export_key varchar(100) NOT NULL,
  export_name varchar(200) NOT NULL,
  points_cost int NOT NULL DEFAULT 0,
  description varchar(500),
  is_active tinyint(1) NOT NULL DEFAULT 1,
  display_order int NOT NULL DEFAULT 999,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY unique_export_key (export_key),
  KEY idx_active (is_active),
  KEY idx_display_order (display_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE recharge_orders (
  id int NOT NULL AUTO_INCREMENT,
  user_id int NOT NULL,
  order_no varchar(64) NOT NULL,
  points_amount int NOT NULL,
  original_price decimal(10,2) NOT NULL,
  discount_price decimal(10,2) NOT NULL,
  payment_method enum('alipay','wechat','mock') DEFAULT 'mock',
  status enum('pending','paid','expired','refunded') DEFAULT 'pending',
  paid_at datetime DEFAULT NULL,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY unique_order_no (order_no),
  KEY idx_user_id (user_id),
  KEY idx_status (status),
  KEY idx_created_at (created_at),
  CONSTRAINT recharge_orders_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE subscription_plans (
  id int NOT NULL AUTO_INCREMENT,
  plan_key varchar(100) NOT NULL,
  plan_name varchar(200) NOT NULL,
  duration_months int NOT NULL,
  original_price decimal(10,2) NOT NULL,
  discount_price decimal(10,2) NOT NULL,
  points_included int NOT NULL DEFAULT 0,
  features json,
  is_active tinyint(1) NOT NULL DEFAULT 1,
  display_order int NOT NULL DEFAULT 999,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY unique_plan_key (plan_key),
  KEY idx_active (is_active),
  KEY idx_display_order (display_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE user_subscriptions (
  id int NOT NULL AUTO_INCREMENT,
  user_id int NOT NULL,
  plan_key varchar(100) NOT NULL,
  order_id int,
  start_date datetime NOT NULL,
  end_date datetime NOT NULL,
  status enum('active','expired','cancelled') NOT NULL DEFAULT 'active',
  auto_renew tinyint(1) NOT NULL DEFAULT 0,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_user_id (user_id),
  KEY idx_status (status),
  KEY idx_end_date (end_date),
  KEY idx_user_status (user_id, status),
  CONSTRAINT user_subscriptions_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  CONSTRAINT user_subscriptions_ibfk_2 FOREIGN KEY (order_id) REFERENCES membership_orders (id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE export_logs (
  id int NOT NULL AUTO_INCREMENT,
  user_id int NOT NULL,
  project_id int,
  export_type varchar(100) NOT NULL,
  points_cost int NOT NULL DEFAULT 0,
  file_size bigint DEFAULT NULL,
  export_status enum('success','failed','processing') NOT NULL DEFAULT 'processing',
  error_message text,
  metadata json,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_user_id (user_id),
  KEY idx_project_id (project_id),
  KEY idx_export_type (export_type),
  KEY idx_created_at (created_at),
  KEY idx_user_created (user_id, created_at DESC),
  CONSTRAINT export_logs_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  CONSTRAINT export_logs_ibfk_2 FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE points_packages (
  id int NOT NULL AUTO_INCREMENT,
  package_key varchar(100) NOT NULL,
  package_name varchar(200) NOT NULL,
  points_amount int NOT NULL,
  original_price decimal(10,2) NOT NULL,
  discount_price decimal(10,2) NOT NULL,
  bonus_points int NOT NULL DEFAULT 0,
  description varchar(500),
  is_active tinyint(1) NOT NULL DEFAULT 1,
  display_order int NOT NULL DEFAULT 999,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY unique_package_key (package_key),
  KEY idx_active (is_active),
  KEY idx_display_order (display_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

ALTER TABLE chat_history ADD COLUMN model_used varchar(100) DEFAULT NULL;
ALTER TABLE chat_history ADD COLUMN points_cost int DEFAULT 0;

ALTER TABLE users ADD INDEX idx_points (points);
ALTER TABLE users ADD INDEX idx_total_earned (total_earned_points);
ALTER TABLE users ADD INDEX idx_total_spent (total_spent_points);
