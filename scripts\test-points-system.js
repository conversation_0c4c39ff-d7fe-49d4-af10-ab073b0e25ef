// 测试积分系统的脚本
// 运行方式: node scripts/test-points-system.js

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'root',
  database: 'loomrun',
  charset: 'utf8mb4'
};

async function testPointsSystem() {
  let connection;
  
  try {
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 检查系统设置
    console.log('\n📋 检查系统设置...');
    const [settings] = await connection.execute(
      'SELECT setting_key, setting_value FROM system_settings WHERE category = "points"'
    );
    console.log('系统设置:', settings);

    // 2. 检查用户表结构
    console.log('\n👤 检查用户表结构...');
    const [userColumns] = await connection.execute(
      'DESCRIBE users'
    );
    const pointsColumns = userColumns.filter(col => 
      col.Field.includes('points') || col.Field.includes('invite')
    );
    console.log('积分相关字段:', pointsColumns.map(col => col.Field));

    // 3. 检查积分相关表
    console.log('\n🗄️ 检查积分相关表...');
    const tables = [
      'points_transactions',
      'user_points_balance', 
      'points_consumption_log',
      'points_expiry_log',
      'ai_models',
      'export_types'
    ];

    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`${table}: ${rows[0].count} 条记录`);
      } catch (error) {
        console.log(`${table}: 表不存在或查询失败`);
      }
    }

    // 4. 检查AI模型配置
    console.log('\n🤖 检查AI模型配置...');
    const [aiModels] = await connection.execute(
      'SELECT model_key, model_name, points_per_request FROM ai_models WHERE is_active = 1'
    );
    console.log('AI模型:', aiModels);

    // 5. 检查导出类型配置
    console.log('\n📤 检查导出类型配置...');
    const [exportTypes] = await connection.execute(
      'SELECT export_key, export_name, points_cost FROM export_types WHERE is_active = 1'
    );
    console.log('导出类型:', exportTypes);

    // 6. 模拟新用户注册积分发放
    console.log('\n🎉 模拟新用户注册积分发放...');
    
    // 创建测试用户
    const testPhone = `test${Date.now()}`;
    const [userResult] = await connection.execute(
      'INSERT INTO users (phone, nickname) VALUES (?, ?)',
      [testPhone, `测试用户${testPhone.slice(-4)}`]
    );
    
    const testUserId = userResult.insertId;
    console.log(`创建测试用户: ID=${testUserId}, 手机号=${testPhone}`);

    // 获取新用户积分配置
    const [pointsConfig] = await connection.execute(
      'SELECT setting_value FROM system_settings WHERE setting_key = "new_user_points_amount"'
    );
    const pointsAmount = parseInt(pointsConfig[0]?.setting_value || '100');

    // 计算有效期
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // 开始事务
    await connection.beginTransaction();

    try {
      // 1. 创建积分余额记录
      const [balanceResult] = await connection.execute(
        `INSERT INTO user_points_balance 
         (user_id, points_type, points_amount, expires_at) 
         VALUES (?, 'activity', ?, ?)`,
        [testUserId, pointsAmount, expiresAt]
      );

      const balanceRecordId = balanceResult.insertId;

      // 2. 创建积分交易记录
      await connection.execute(
        `INSERT INTO points_transactions 
         (user_id, transaction_type, points_amount, balance_before, balance_after, source_type, points_type, expires_at, balance_record_id, description) 
         VALUES (?, 'earn', ?, 0, ?, 'registration', 'activity', ?, ?, ?)`,
        [testUserId, pointsAmount, pointsAmount, expiresAt, balanceRecordId, `新用户注册奖励 ${pointsAmount} 积分`]
      );

      // 3. 更新用户积分
      await connection.execute(
        'UPDATE users SET points = ?, total_earned_points = ? WHERE id = ?',
        [pointsAmount, pointsAmount, testUserId]
      );

      // 提交事务
      await connection.commit();
      console.log(`✅ 积分发放成功: 用户${testUserId} 获得 ${pointsAmount} 积分，有效期至 ${expiresAt.toLocaleDateString()}`);

      // 验证结果
      const [userInfo] = await connection.execute(
        'SELECT points, total_earned_points FROM users WHERE id = ?',
        [testUserId]
      );
      console.log('用户积分信息:', userInfo[0]);

      const [transactionInfo] = await connection.execute(
        'SELECT * FROM points_transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1',
        [testUserId]
      );
      console.log('最新交易记录:', transactionInfo[0]);

    } catch (error) {
      await connection.rollback();
      throw error;
    }

    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    await connection.execute('DELETE FROM users WHERE id = ?', [testUserId]);
    console.log('测试数据清理完成');

    console.log('\n🎉 积分系统测试完成！所有功能正常');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testPointsSystem();
