import Dysmsapi20170525, * as $Dysmsapi20170525 from '@alicloud/dysmsapi20170525';
import * as $OpenApi from '@alicloud/openapi-client';
import * as $Util from '@alicloud/tea-util';
import * as $Credential from '@alicloud/credentials';

// 阿里云短信服务配置
interface SmsConfig {
  accessKeyId: string;
  accessKeySecret: string;
  signName: string;
  templateCode: string;
  endpoint: string;
}

// 从环境变量获取配置
const getSmsConfig = (): SmsConfig => {
  const config = {
    accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET || '',
    signName: process.env.ALIYUN_SMS_SIGN_NAME || '万来云边',
    templateCode: process.env.ALIYUN_SMS_TEMPLATE_CODE || 'SMS_324480375',
    endpoint: 'dysmsapi.aliyuncs.com'
  };

  // 验证必要的配置
  if (!config.accessKeyId || !config.accessKeySecret) {
    throw new Error('阿里云短信服务配置不完整，请检查环境变量 ALIYUN_ACCESS_KEY_ID 和 ALIYUN_ACCESS_KEY_SECRET');
  }

  return config;
};

// 创建阿里云短信客户端
const createSmsClient = (): Dysmsapi20170525 => {
  const config = getSmsConfig();
  
  // 使用凭据初始化账号Client
  const credential = new $Credential.default({
    accessKeyId: config.accessKeyId,
    accessKeySecret: config.accessKeySecret,
  });

  const openApiConfig = new $OpenApi.Config({
    credential: credential,
  });
  
  openApiConfig.endpoint = config.endpoint;
  
  return new Dysmsapi20170525(openApiConfig);
};

// 发送短信验证码
export const sendSmsCode = async (phone: string, code: string): Promise<{ success: boolean; message: string; bizId?: string }> => {
  try {
    const config = getSmsConfig();
    const client = createSmsClient();
    
    const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
      signName: config.signName,
      templateCode: config.templateCode,
      phoneNumbers: phone,
      templateParam: JSON.stringify({ code }),
      smsUpExtendCode: '',
    });

    const runtime = new $Util.RuntimeOptions({});
    
    console.log(`正在发送短信验证码到 ${phone}，验证码: ${code}`);
    
    const response = await client.sendSmsWithOptions(sendSmsRequest, runtime);
    
    console.log('阿里云短信API响应:', {
      Code: response.body.code,
      Message: response.body.message,
      BizId: response.body.bizId,
      RequestId: response.body.requestId
    });

    if (response.body.code === 'OK') {
      return {
        success: true,
        message: '短信发送成功',
        bizId: response.body.bizId
      };
    } else {
      return {
        success: false,
        message: response.body.message || '短信发送失败'
      };
    }
  } catch (error: any) {
    console.error('发送短信验证码失败:', error);
    
    // 处理阿里云API错误
    if (error.data && error.data.Recommend) {
      console.log('阿里云错误诊断地址:', error.data.Recommend);
    }
    
    return {
      success: false,
      message: error.message || '短信发送失败，请稍后重试'
    };
  }
};

// 验证手机号格式
export const validatePhoneNumber = (phone: string): boolean => {
  // 中国大陆手机号正则表达式
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 生成6位数字验证码
export const generateSmsCode = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 短信发送频率限制检查
export const checkSmsRateLimit = async (phone: string): Promise<{ canSend: boolean; waitTime?: number }> => {
  // 这里可以实现基于Redis或数据库的频率限制
  // 暂时返回允许发送
  return { canSend: true };
};

export default {
  sendSmsCode,
  validatePhoneNumber,
  generateSmsCode,
  checkSmsRateLimit
};
