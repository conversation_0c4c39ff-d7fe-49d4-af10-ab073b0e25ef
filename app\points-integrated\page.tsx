"use client";

import { useState, useEffect } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Header } from "@/components/editor/header";
import { 
  Coins, 
  TrendingUp, 
  TrendingDown, 
  History, 
  ArrowLeft,
  RefreshCw,
  Clock,
  Gift,
  Zap,
  Download,
  Users,
  CreditCard,
  Calendar,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";

interface PointsTransaction {
  id: number;
  transaction_type: 'earn' | 'spend';
  points_amount: number;
  balance_before: number;
  balance_after: number;
  source_type: string;
  points_type: string;
  description: string;
  expires_at: string | null;
  created_at: string;
}

export default function PointsIntegratedPage() {
  const { user, loading } = useUser();
  const router = useRouter();
  const [transactions, setTransactions] = useState<PointsTransaction[]>([]);
  const [transactionsLoading, setTransactionsLoading] = useState(false);

  // 获取积分历史
  const fetchTransactions = async () => {
    setTransactionsLoading(true);
    try {
      const response = await fetch('/api/points/history?limit=20');
      const data = await response.json();
      if (data.success) {
        setTransactions(data.data.history);
      } else {
        toast.error('获取积分历史失败');
      }
    } catch (error) {
      console.error('获取积分历史失败:', error);
      toast.error('获取积分历史失败');
    } finally {
      setTransactionsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchTransactions();
    }
  }, [user]);

  // 如果用户未登录，重定向到首页
  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
    }
  }, [user, loading, router]);

  const getSourceTypeLabel = (sourceType: string) => {
    const labels: Record<string, string> = {
      registration: '注册奖励',
      ai_request: 'AI请求',
      export: '项目导出',
      invitation: '邀请奖励',
      recharge: '充值',
      subscription: '订阅',
      admin_adjust: '管理员调整',
      refund: '退款'
    };
    return labels[sourceType] || sourceType;
  };

  const getSourceTypeIcon = (sourceType: string) => {
    const icons: Record<string, React.ReactNode> = {
      registration: <Gift className="w-4 h-4" />,
      ai_request: <Zap className="w-4 h-4" />,
      export: <Download className="w-4 h-4" />,
      invitation: <Users className="w-4 h-4" />,
      recharge: <CreditCard className="w-4 h-4" />,
      subscription: <Calendar className="w-4 h-4" />,
      admin_adjust: <AlertCircle className="w-4 h-4" />,
      refund: <RefreshCw className="w-4 h-4" />
    };
    return icons[sourceType] || <Coins className="w-4 h-4" />;
  };

  const getPointsTypeColor = (pointsType: string) => {
    const colors: Record<string, string> = {
      activity: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      subscription: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      recharge: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
    };
    return colors[pointsType] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  };

  const getPointsTypeLabel = (pointsType: string) => {
    const labels: Record<string, string> = {
      activity: '活动积分',
      subscription: '订阅积分',
      recharge: '充值积分'
    };
    return labels[pointsType] || pointsType;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="h-screen bg-background flex flex-col">
        <Header onLogoClick={() => router.push('/')} />
        <div className="flex-1 flex items-center justify-center">
          <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="h-screen bg-background flex flex-col">
      {/* 使用系统的 Header 组件 */}
      <Header onLogoClick={() => router.push('/')} />

      {/* 主布局区域 - 左侧工具栏 + 右侧内容区 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧工具栏 */}
        <div className="w-80 bg-background border-r border-border flex flex-col">
          {/* 工具栏标题 */}
          <div className="p-6 border-b border-border">
            <h2 className="text-xl font-semibold flex items-center gap-3">
              <Coins className="w-6 h-6 text-blue-600" />
              积分管理
            </h2>
            <p className="text-sm text-muted-foreground mt-1">管理和查看您的积分详情</p>
          </div>

          {/* 积分概览 */}
          <div className="p-6 space-y-4">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">当前积分</p>
                  <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{user.points?.toLocaleString() || 0}</p>
                </div>
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <Coins className="w-5 h-5 text-white" />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50 border border-green-200 dark:border-green-800 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <p className="text-xs font-medium text-green-600 dark:text-green-400">累计获得</p>
                </div>
                <p className="text-lg font-bold text-green-700 dark:text-green-300">{user.total_earned_points?.toLocaleString() || 0}</p>
              </div>

              <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/50 dark:to-red-900/50 border border-red-200 dark:border-red-800 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <TrendingDown className="w-4 h-4 text-red-600" />
                  <p className="text-xs font-medium text-red-600 dark:text-red-400">累计消费</p>
                </div>
                <p className="text-lg font-bold text-red-700 dark:text-red-300">{user.total_spent_points?.toLocaleString() || 0}</p>
              </div>
            </div>
          </div>

          {/* 快捷操作 */}
          <div className="p-6 border-t border-border">
            <h3 className="text-sm font-semibold text-foreground mb-3">快捷操作</h3>
            <div className="space-y-2">
              <Button variant="ghost" className="w-full justify-start text-sm" onClick={() => fetchTransactions()}>
                <RefreshCw className="w-4 h-4 mr-2" />
                刷新记录
              </Button>
              <Button variant="ghost" className="w-full justify-start text-sm" onClick={() => router.push('/recharge')}>
                <CreditCard className="w-4 h-4 mr-2" />
                充值积分
              </Button>
              <Button variant="ghost" className="w-full justify-start text-sm" onClick={() => router.push('/invite')}>
                <Users className="w-4 h-4 mr-2" />
                邀请好友
              </Button>
            </div>
          </div>
        </div>

        {/* 右侧内容区 */}
        <div className="flex-1 overflow-y-auto bg-muted/30">
          <div className="p-8">
            {/* 积分历史标题 */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold flex items-center gap-3">
                <History className="w-6 h-6 text-foreground" />
                积分变动历史
              </h1>
              <p className="text-muted-foreground mt-1">查看您的积分获得和消费记录</p>
            </div>

            {/* 积分历史内容 */}
            <div className="bg-background rounded-lg border border-border">
              <div className="p-6">
              {transactionsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
                </div>
              ) : transactions.length > 0 ? (
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          transaction.transaction_type === 'earn' 
                            ? 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400' 
                            : 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400'
                        }`}>
                          {getSourceTypeIcon(transaction.source_type)}
                        </div>
                        <div>
                          <div className="font-medium">{transaction.description}</div>
                          <div className="text-sm text-muted-foreground flex items-center gap-2">
                            <span>{getSourceTypeLabel(transaction.source_type)}</span>
                            {transaction.points_type && (
                              <Badge className={getPointsTypeColor(transaction.points_type)} variant="secondary">
                                {getPointsTypeLabel(transaction.points_type)}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${
                          transaction.transaction_type === 'earn' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.transaction_type === 'earn' ? '+' : '-'}{transaction.points_amount}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          余额: {transaction.balance_after}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatDate(transaction.created_at)}
                        </div>
                        {transaction.expires_at && (
                          <div className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                            <Clock className="w-3 h-3" />
                            有效期至: {new Date(transaction.expires_at).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  <History className="w-16 h-16 mx-auto mb-4 opacity-30" />
                  <p className="text-lg font-medium mb-2">暂无积分变动记录</p>
                  <p className="text-sm">完成任务或充值后，积分记录将显示在这里</p>
                </div>
              )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
