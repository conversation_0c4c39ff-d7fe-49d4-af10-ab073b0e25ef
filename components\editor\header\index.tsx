import { ReactNode, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { TopUserMenu } from "@/components/editor/top-user-menu";
import { ShareButton } from "@/components/editor/share-button";
import ContactChat from "@/components/contact-chat";
import { GhostLogo } from "@/components/ui/ghost-logo";

export function Header({
  children,
  onLogoClick,
  project,
  htmlContent,
  onMobileSidebarToggle,
}: {
  children?: ReactNode;
  onLogoClick?: () => void;
  project?: { id: number; title?: string };
  htmlContent?: string;
  onMobileSidebarToggle?: () => void;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [isChatOpen, setIsChatOpen] = useState(false);

  // 🎯 智能Logo点击处理
  const handleLogoClick = () => {
    const startTime = performance.now();
    
    // 🔍 如果已经在欢迎界面，不执行任何操作
    if (pathname === '/projects/new') {
      console.log('🏠 已在欢迎界面，忽略Logo点击');
      
      // 📊 性能日志
      if (process.env.NODE_ENV === 'development') {
        const duration = performance.now() - startTime;
        console.log(`⚡ Logo点击: 已在欢迎界面，无操作 (${duration.toFixed(1)}ms)`);
      }
      return;
    }
    
    // 🚀 在其他页面：执行跳转到欢迎界面的操作
    if (onLogoClick) {
      // 使用传入的自定义处理函数
      onLogoClick();
    } else {
      // 默认跳转到欢迎界面
      router.push('/projects/new');
    }
    
    // 📊 性能日志
    if (process.env.NODE_ENV === 'development') {
      const duration = performance.now() - startTime;
      console.log(`⚡ Logo点击: 跳转到欢迎界面 (${duration.toFixed(1)}ms)`);
    }
  };

  return (
    <>
             <header className="border-b bg-background/95 backdrop-blur-md border-border px-0.5 md:px-1 lg:px-2 grid grid-cols-3 z-50 relative flex-shrink-0 h-11 w-full">
        <div className="flex items-center justify-start gap-1 md:gap-2">
          {/* 移动端汉堡菜单按钮 - 完全透明，无边框，向上调整4% */}
          <button
            onClick={onMobileSidebarToggle}
            className="lg:hidden w-7 h-7 md:w-10 md:h-10 flex items-center justify-center hover:bg-secondary/40 active:bg-secondary/60 transition-all duration-200 group hamburger-button border-0 bg-transparent outline-none focus:outline-none"
            style={{
              WebkitTapHighlightColor: 'transparent',
              touchAction: 'manipulation',
              border: 'none',
              boxShadow: 'none',
              transform: 'translateY(-4%)'
            }}
            aria-label="打开菜单"
            title="打开侧边栏"
          >
            {/* 专业汉堡图标 - 深浅模式优化 */}
            <svg
              className="w-4 h-4 md:w-5 md:h-5 text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-200"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              strokeWidth={2.5}
            >
              <g>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 6h18"
                  className="origin-center transition-transform duration-200"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 12h18"
                  className="origin-center transition-opacity duration-200"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 18h18"
                  className="origin-center transition-transform duration-200"
                />
              </g>
            </svg>
          </button>

          {/* 专业斜杠分隔符 - 精确居中定位 */}
          <div className="lg:hidden flex items-center justify-center" style={{ width: '16px', height: '44px' }}>
            <div
              className="w-px bg-gray-400 dark:bg-gray-500"
              style={{
                height: '16px',
                transform: 'rotate(20deg)',
                position: 'relative'
              }}
            />
          </div>
          <h1 className="text-foreground text-sm font-bold flex items-center justify-start">
            <button
              onClick={handleLogoClick}
              className={`flex items-center gap-1.5 transition-all duration-200 focus:outline-none p-0 ${
                pathname === '/projects/new'
                  ? 'cursor-default opacity-90' // 在欢迎界面时的视觉状态
                  : 'hover:opacity-80 cursor-pointer' // 可点击状态
              }`}
              title={
                pathname === '/projects/new'
                  ? '当前已在欢迎界面'
                  : '返回欢迎界面'
              }
              style={{
                marginLeft: '-6px' // 🎯 优化后的斜杠分隔符，调整左边距
              }}
            >
              {/* 🎯 使用小精灵Logo + LoomRun文字 */}
              <div
                className="flex items-center gap-2 px-1 overflow-hidden focus-within:ring-2 focus-within:ring-blue-500/50 rounded-lg group"
                style={{
                  height: '40px' // 🎯 保持高度不变
                }}
              >
                {/* 小精灵图标 */}
                <div className="transition-all duration-300 ease-out group-hover:scale-110 group-hover:-translate-y-0.5 group-hover:drop-shadow-lg">
                  <GhostLogo
                    size={28}
                    className={`transition-all duration-300 ${
                      pathname === '/projects/new' ? '' : ''
                    }`}
                  />
                </div>

                {/* LoomRun文字 - 响应式显示 */}
                <div className="hidden sm:block">
                  <span
                    className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 dark:from-blue-400 dark:via-purple-400 dark:to-blue-600 bg-clip-text text-transparent font-bold text-base sm:text-lg tracking-tight transition-all duration-300 group-hover:from-blue-700 group-hover:via-purple-700 group-hover:to-blue-900 dark:group-hover:from-blue-300 dark:group-hover:via-purple-300 dark:group-hover:to-blue-500 drop-shadow-sm"
                    style={{
                      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                      fontWeight: '700',
                      letterSpacing: '-0.025em'
                    }}
                  >
                    LoomRun
                  </span>
                </div>
              </div>
            </button>
          </h1>
        </div>
        <div className="flex items-center justify-center">
          {/* 空白中间区域 */}
        </div>
        <div className="flex items-center justify-end gap-1">
          {project && htmlContent && (
            <ShareButton 
              projectId={project.id}
              htmlContent={htmlContent}
              projectTitle={project.title}
            />
          )}
          {children}
          <TopUserMenu 
            isChatOpen={isChatOpen}
            setIsChatOpen={setIsChatOpen}
          />
        </div>
      </header>
      
      {/* 联系我们弹窗 - 完全独立于Header容器，直接相对于视口定位 */}
      <ContactChat 
        isOpen={isChatOpen} 
        onClose={() => setIsChatOpen(false)} 
      />
    </>
  );
}
