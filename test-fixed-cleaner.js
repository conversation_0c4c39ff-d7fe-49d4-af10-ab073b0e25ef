// 测试修复后的TailwindCleaner
class TailwindCleaner {
  
  // 检测Tailwind CDN脚本标签的正则表达式
  static TAILWIND_CDN_REGEX = /<script[^>]*src=["'][^"']*cdn\.tailwindcss\.com[^"']*["'][^>]*>[\s\S]*?<\/script>/gi;
  
  // 更强的Tailwind重置样式检测 - 匹配包含大量Tailwind变量的样式块
  static TAILWIND_FULL_RESET_REGEX = /<style[^>]*>[\s\S]*?(?:--tw-border-spacing-x|--tw-translate-x|--tw-rotate|--tw-ring-inset|tailwindcss v\d+\.\d+\.\d+|\*,::after,::before\{box-sizing:border-box)[\s\S]*?<\/style>/gi;

  static cleanHTML(html) {
    if (!html || !html.trim()) {
      return html;
    }

    console.log('🧹 开始清理Tailwind CSS重复内容...');
    
    let cleanedHtml = html;
    let changeCount = 0;

    // 1. 清理重复的Tailwind CDN脚本标签
    const cdnMatches = html.match(this.TAILWIND_CDN_REGEX);
    if (cdnMatches && cdnMatches.length > 1) {
      console.log(`🔍 发现 ${cdnMatches.length} 个Tailwind CDN脚本标签，保留第一个`);
      
      // 保留第一个，移除其余的
      let cdnCount = 0;
      cleanedHtml = cleanedHtml.replace(this.TAILWIND_CDN_REGEX, (match) => {
        cdnCount++;
        if (cdnCount === 1) {
          return match; // 保留第一个
        }
        changeCount++;
        return ''; // 移除重复的
      });
    }

    // 2. 检测并移除所有Tailwind重置样式块（因为我们有CDN）
    const resetMatches = cleanedHtml.match(this.TAILWIND_FULL_RESET_REGEX);
    if (resetMatches && resetMatches.length > 0) {
      console.log(`⚠️ 检测到 ${resetMatches.length} 个Tailwind重置样式块，这些应该被CDN处理，正在移除...`);
      
      resetMatches.forEach((match, index) => {
        console.log(`📋 样式块 ${index + 1}: ${match.substring(0, 100)}...`);
      });

      // 移除所有Tailwind重置样式块
      cleanedHtml = cleanedHtml.replace(this.TAILWIND_FULL_RESET_REGEX, () => {
        changeCount++;
        return ''; // 移除所有匹配的样式块
      });
    }

    // 3. 清理空的style标签
    cleanedHtml = cleanedHtml.replace(/<style[^>]*>\s*<\/style>/gi, '');

    // 4. 清理多余的空行
    cleanedHtml = cleanedHtml.replace(/\n\s*\n\s*\n/g, '\n\n');

    if (changeCount > 0) {
      console.log(`✅ Tailwind清理完成，移除了 ${changeCount} 个重复/冗余项`);
    } else {
      console.log('✅ 未发现Tailwind重复内容');
    }

    return cleanedHtml;
  }
}

// 用户的真实HTML（简化版本用于测试）
const userHTML = `<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Number Display</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style type="text/tailwindcss">
      @layer utilities {
      .content-auto {
      content-visibility: auto;
      }
      .text-shadow {
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      }
    </style>
    <style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */</style>
    <style id="loomrun-custom-styles" type="text/css">.custom-style-1 {
      color: rgb(59, 130, 246);
      background-color: #ef4444;
      font-size: 89.5px;
      font-weight: 700;
      text-align: center;
      border: 0px solid rgb(229, 231, 235);
      }</style>
  </head>
  <body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen flex items-center justify-center p-4">
    <div class="text-center">
      <h1 class="custom-style-1">458484</h1>
    </div>
  </body>
</html>`;

console.log('🚀 测试修复后的TailwindCleaner');
console.log('==========================================');
console.log(`📊 原始HTML长度: ${userHTML.length}`);

const cleanedHTML = TailwindCleaner.cleanHTML(userHTML);

console.log(`📊 清理后HTML长度: ${cleanedHTML.length}`);
console.log(`📈 减少了 ${userHTML.length - cleanedHTML.length} 个字符`);
console.log(`📉 文件大小减少: ${Math.round((1 - cleanedHTML.length / userHTML.length) * 100)}%`);

console.log('\n📝 清理后的HTML结果:');
console.log('==========================================');
console.log(cleanedHTML);

console.log('\n🔍 验证结果:');
console.log('==========================================');
console.log(`Tailwind CDN保留: ${cleanedHTML.includes('cdn.tailwindcss.com') ? '✅' : '❌'}`);
console.log(`自定义样式保留: ${cleanedHTML.includes('loomrun-custom-styles') ? '✅' : '❌'}`);
console.log(`工具类样式保留: ${cleanedHTML.includes('text-shadow') ? '✅' : '❌'}`);
console.log(`重置样式清理: ${!cleanedHTML.includes('--tw-border-spacing-x') ? '✅' : '❌'}`); 