export const PROVIDERS = {
  "deepseek-official": {
    name: "DeepSeek Official",
    max_tokens: 8192,
    id: "deepseek-official",
    baseURL: "https://api.deepseek.com",
  },
  "doubao-official": {
    name: "Doubao Official",
    max_tokens: 32768,
    id: "doubao-official",
    baseURL: "https://ark.cn-beijing.volces.com/api/v3",
  },
} as const;

type ProviderKey = keyof typeof PROVIDERS;

export const MODELS = [
  {
    value: "deepseek-chat",
    label: "DeepSeek V3 Chat",
    providers: ["deepseek-official"] as Provider<PERSON><PERSON>[],
    autoProvider: "deepseek-official" as Provider<PERSON><PERSON>,
  },
  {
    value: "deepseek-reasoner",
    label: "DeepSeek R1 Reasoner",
    providers: ["deepseek-official"] as Provider<PERSON>ey[],
    autoProvider: "deepseek-official" as Provider<PERSON><PERSON>,
    isNew: true,
    isThinker: true,
  },
  {
    value: "doubao-seed-1-6-250615",
    label: "Doubao Vision Pro",
    providers: ["doubao-official"] as Provider<PERSON><PERSON>[],
    autoProvider: "doubao-official" as Provider<PERSON><PERSON>,
    supportsImages: true,
  },
  {
    value: "claude-3.5-sonnet",
    label: "Claude-3.5 Sonnet",
    providers: [] as ProviderKey[],
    autoProvider: "" as string,
    isComingSoon: true,
  },
  {
    value: "claude-4-sonnet",
    label: "Claude-4 Sonnet",
    providers: [] as ProviderKey[],
    autoProvider: "" as string,
    isComingSoon: true,
  },
  {
    value: "gemini-2.0-flash",
    label: "Gemini 2.0 Flash",
    providers: [] as ProviderKey[],
    autoProvider: "" as string,
    isComingSoon: true,
  },
];
