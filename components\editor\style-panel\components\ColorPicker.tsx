"use client";
import { Input } from "@/components/ui/input";

interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
  presets?: string[];
}

export function ColorPicker({ value, onChange, presets }: ColorPickerProps) {
  const commonColors = presets || [
    '#000000', '#ffffff', '#3b82f6', '#ef4444', 
    '#10b981', '#f59e0b', '#8b5cf6', '#f97316'
  ];

  return (
    <div className="space-y-2">
      {/* 主要颜色选择器 */}
      <div className="flex items-center space-x-2">
        <input
          type="color"
          value={value || '#000000'}
          onChange={(e) => onChange(e.target.value)}
          className="w-7 h-7 rounded border border-border cursor-pointer bg-background shadow-sm hover:shadow-md transition-shadow"
        />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="#000000"
          className="flex-1 h-7 text-xs font-mono bg-background border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
        />
      </div>
      
      {/* 快速预设颜色 - 更紧凑 */}
      <div className="flex flex-wrap gap-1">
        {commonColors.slice(0, 6).map(color => (
          <button
            key={color}
            onClick={() => onChange(color)}
            className={`w-5 h-5 rounded border transition-all duration-200 hover:scale-110 ${
              value === color
                ? 'border-primary border-2 shadow-sm'
                : 'border-border hover:border-primary/50'
            }`}
            style={{ backgroundColor: color === 'transparent' ? 'transparent' : color }}
            title={color}
          >
            {color === 'transparent' && (
              <div className="w-full h-full bg-gradient-to-br from-red-500/20 to-red-500/20 rounded flex items-center justify-center">
                <div className="w-2 h-0.5 bg-red-500 rotate-45"></div>
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
} 