"use client";

import { useUser } from "@/loomrunhooks/useUser";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Header } from "@/components/editor/header";
import { 
  Coins, 
  ExternalLink, 
  User, 
  CheckCircle, 
  Database, 
  Calendar,
  Gift,
  CreditCard,
  Clock,
  AlertTriangle
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function TestPointsRealPage() {
  const { user } = useUser();
  const router = useRouter();

  return (
    <div className="h-screen bg-background flex flex-col">
      {/* 使用系统的 Header 组件 */}
      <Header onLogoClick={() => router.push('/')} />
      
      {/* 主内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          <div className="flex items-center gap-2 mb-6">
            <Database className="w-6 h-6" />
            <h1 className="text-2xl font-bold">真实积分数据展示测试</h1>
          </div>

          {/* 用户状态 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                用户积分状态
              </CardTitle>
              <CardDescription>基于真实数据库表结构的积分信息</CardDescription>
            </CardHeader>
            <CardContent>
              {user ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{user.points || 0}</div>
                      <div className="text-sm text-muted-foreground">当前总积分</div>
                      <div className="text-xs text-muted-foreground mt-1">users.points</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{user.total_earned_points || 0}</div>
                      <div className="text-sm text-muted-foreground">累计获得</div>
                      <div className="text-xs text-muted-foreground mt-1">users.total_earned_points</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{user.total_spent_points || 0}</div>
                      <div className="text-sm text-muted-foreground">累计消费</div>
                      <div className="text-xs text-muted-foreground mt-1">users.total_spent_points</div>
                    </div>
                  </div>
                  
                  <Link href="/points-pro">
                    <Button className="flex items-center gap-2">
                      <Coins className="w-4 h-4" />
                      查看真实积分详情
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">请先登录以查看积分信息</p>
                  <Link href="/">
                    <Button>前往首页登录</Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 数据库表结构说明 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                数据库表结构
              </CardTitle>
              <CardDescription>积分系统的真实数据库设计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-blue-600 flex items-center gap-2">
                    <Database className="w-4 h-4" />
                    user_points_balance 表
                  </h4>
                  <div className="bg-muted/50 p-3 rounded-lg text-sm font-mono">
                    <div className="space-y-1">
                      <div>• id (主键)</div>
                      <div>• user_id (用户ID)</div>
                      <div className="text-green-600">• points_type (积分类型)</div>
                      <div className="text-blue-600">• points_amount (积分数量)</div>
                      <div className="text-orange-600">• expires_at (有效期)</div>
                      <div>• is_active (是否有效)</div>
                      <div>• created_at (创建时间)</div>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-600 flex items-center gap-2">
                    <Database className="w-4 h-4" />
                    points_transactions 表
                  </h4>
                  <div className="bg-muted/50 p-3 rounded-lg text-sm font-mono">
                    <div className="space-y-1">
                      <div>• id (主键)</div>
                      <div>• user_id (用户ID)</div>
                      <div>• transaction_type (交易类型)</div>
                      <div>• points_amount (积分数量)</div>
                      <div>• source_type (来源类型)</div>
                      <div className="text-green-600">• points_type (积分类型)</div>
                      <div className="text-orange-600">• expires_at (有效期)</div>
                      <div>• description (描述)</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 积分类型说明 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>积分类型详解</CardTitle>
              <CardDescription>三种积分类型的特点和有效期规则</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                      <Gift className="w-4 h-4 text-white" />
                    </div>
                    <h5 className="font-semibold text-green-800 dark:text-green-200">活动积分</h5>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>注册奖励</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>邀请奖励</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-orange-600" />
                      <span>有效期30天</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <Calendar className="w-4 h-4 text-white" />
                    </div>
                    <h5 className="font-semibold text-blue-800 dark:text-blue-200">订阅积分</h5>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-blue-600" />
                      <span>会员订阅</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-blue-600" />
                      <span>套餐赠送</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-orange-600" />
                      <span>按订阅周期</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                      <CreditCard className="w-4 h-4 text-white" />
                    </div>
                    <h5 className="font-semibold text-purple-800 dark:text-purple-200">充值积分</h5>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-purple-600" />
                      <span>用户充值</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-purple-600" />
                      <span>购买套餐</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>永久有效</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 新设计特性 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                新设计的专业特性
              </CardTitle>
              <CardDescription>基于真实数据库的用户友好设计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-600">用户真正关心的信息</h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>当前可用总积分（不是历史统计）</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>按类型显示积分余额（活动/订阅/充值）</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>每种类型的具体有效期</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-orange-500 mt-0.5" />
                      <span>即将过期的积分提醒</span>
                    </li>
                  </ul>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-blue-600">移除的冗余信息</h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="w-4 h-4 bg-red-500 rounded-full mt-0.5"></div>
                      <span>累计获得积分（用户不关心历史总数）</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-4 h-4 bg-red-500 rounded-full mt-0.5"></div>
                      <span>累计消费积分（用户不关心历史总数）</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>专注于当前可用积分和有效期</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>提供实用的积分管理信息</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* API 接口说明 */}
          <Card>
            <CardHeader>
              <CardTitle>API 接口实现</CardTitle>
              <CardDescription>基于真实数据库表的接口设计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <h5 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">GET /api/points/balance</h5>
                  <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                    获取用户积分余额详情，按类型分组统计
                  </p>
                  <div className="bg-muted/50 p-2 rounded text-xs font-mono">
                    SELECT points_type, SUM(points_amount) as total_points, MIN(expires_at) as earliest_expiry<br/>
                    FROM user_points_balance WHERE user_id = ? AND is_active = 1<br/>
                    GROUP BY points_type
                  </div>
                </div>
                
                <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <h5 className="font-semibold text-green-800 dark:text-green-200 mb-2">实时有效期检查</h5>
                  <p className="text-sm text-green-700 dark:text-green-300 mb-2">
                    自动检查即将过期的积分（7天内）并提醒用户
                  </p>
                  <div className="bg-muted/50 p-2 rounded text-xs font-mono">
                    WHERE expires_at IS NOT NULL AND expires_at &lt;= DATE_ADD(NOW(), INTERVAL 7 DAY)
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 测试按钮 */}
          <div className="mt-8 flex gap-4 flex-wrap">
            <Link href="/points-pro">
              <Button className="flex items-center gap-2">
                <Coins className="w-4 h-4" />
                查看真实积分详情页面
              </Button>
            </Link>
            
            <Button variant="outline" onClick={() => window.open('/api/points/balance', '_blank')}>
              <Database className="w-4 h-4 mr-2" />
              测试积分余额API
            </Button>
            
            <Button variant="outline" onClick={() => window.open('/api/points/history', '_blank')}>
              <Database className="w-4 h-4 mr-2" />
              测试积分历史API
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
