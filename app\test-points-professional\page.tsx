"use client";

import { useUser } from "@/loomrunhooks/useUser";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Header } from "@/components/editor/header";
import { 
  Database, 
  User, 
  CheckCircle, 
  TrendingUp,
  TrendingDown,
  History,
  Filter,
  Clock,
  Gift,
  Calendar,
  CreditCard,
  Zap,
  Download,
  Users,
  AlertCircle
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function TestPointsProfessionalPage() {
  const { user } = useUser();
  const router = useRouter();

  return (
    <div className="h-screen bg-background flex flex-col">
      <Header onLogoClick={() => router.push('/')} />
      
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          <div className="flex items-center gap-2 mb-6">
            <Database className="w-6 h-6" />
            <h1 className="text-2xl font-bold">专业积分详情系统测试</h1>
          </div>

          {/* 用户状态 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                当前用户状态
              </CardTitle>
              <CardDescription>验证专业积分详情功能</CardDescription>
            </CardHeader>
            <CardContent>
              {user ? (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{user.id}</div>
                    <div className="text-sm text-muted-foreground">用户ID</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{user.points || 0}</div>
                    <div className="text-sm text-muted-foreground">当前积分</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="text-lg font-bold text-purple-600">{user.total_earned_points || 0}</div>
                    <div className="text-sm text-muted-foreground">累计获得</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <div className="text-lg font-bold text-orange-600">{user.total_spent_points || 0}</div>
                    <div className="text-sm text-muted-foreground">累计消费</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">请先登录以查看积分信息</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 新功能特性 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                专业积分详情系统特性
              </CardTitle>
              <CardDescription>基于真实数据库的高效查询和专业展示</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-blue-600 flex items-center gap-2">
                    <History className="w-4 h-4" />
                    变更记录功能
                  </h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>支持多维度筛选（类型、来源、日期）</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>实时有效期状态显示</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>即将过期积分提醒</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>详细的余额变化记录</span>
                    </li>
                  </ul>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-600 flex items-center gap-2">
                    <TrendingDown className="w-4 h-4" />
                    消耗详情功能
                  </h4>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>详细的积分消耗记录</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>消耗来源和原因追踪</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>原始积分有效期信息</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                      <span>过期积分消耗标识</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* API 优化特性 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                API 查询优化
              </CardTitle>
              <CardDescription>高效的数据库查询和数据处理</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-blue-600">GET /api/points/history</h4>
                  <div className="bg-muted/50 p-3 rounded-lg text-sm">
                    <div className="space-y-1">
                      <div className="text-green-600">✅ 支持多维度筛选参数</div>
                      <div className="text-green-600">✅ 优化的分页查询</div>
                      <div className="text-green-600">✅ 左连接获取有效期信息</div>
                      <div className="text-green-600">✅ 实时计算过期状态</div>
                      <div className="text-green-600">✅ 格式化显示数据</div>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-600">GET /api/points/consumption</h4>
                  <div className="bg-muted/50 p-3 rounded-lg text-sm">
                    <div className="space-y-1">
                      <div className="text-green-600">✅ 详细的消耗记录查询</div>
                      <div className="text-green-600">✅ 关联交易和余额信息</div>
                      <div className="text-green-600">✅ 消耗统计数据</div>
                      <div className="text-green-600">✅ 过期状态判断</div>
                      <div className="text-green-600">✅ 多表联查优化</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 筛选功能展示 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="w-5 h-5" />
                筛选功能展示
              </CardTitle>
              <CardDescription>支持的筛选条件和参数</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h5 className="font-medium text-blue-600">交易类型筛选</h5>
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-3 h-3 text-green-600" />
                      <span>获得记录 (earn)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingDown className="w-3 h-3 text-red-600" />
                      <span>消费记录 (spend)</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h5 className="font-medium text-green-600">积分类型筛选</h5>
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center gap-2">
                      <Gift className="w-3 h-3 text-green-600" />
                      <span>活动积分</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-3 h-3 text-blue-600" />
                      <span>订阅积分</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CreditCard className="w-3 h-3 text-purple-600" />
                      <span>充值积分</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h5 className="font-medium text-purple-600">来源类型筛选</h5>
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center gap-2">
                      <Gift className="w-3 h-3" />
                      <span>注册奖励</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Zap className="w-3 h-3" />
                      <span>AI请求</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Download className="w-3 h-3" />
                      <span>项目导出</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="w-3 h-3" />
                      <span>邀请奖励</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      <span>管理员调整</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 状态指示器 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                状态指示器
              </CardTitle>
              <CardDescription>积分有效期和状态的可视化展示</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="destructive" className="text-xs">即将过期</Badge>
                    <span className="text-sm">7天内过期</span>
                  </div>
                  <p className="text-xs text-muted-foreground">红色标签提醒用户尽快使用</p>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-600">已过期</Badge>
                    <span className="text-sm">过期积分</span>
                  </div>
                  <p className="text-xs text-muted-foreground">灰色标签显示已过期积分</p>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-600">过期消耗</Badge>
                    <span className="text-sm">过期后消耗</span>
                  </div>
                  <p className="text-xs text-muted-foreground">橙色标签标识过期积分的消耗</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 测试按钮 */}
          <div className="flex gap-4 flex-wrap">
            <Link href="/points-pro">
              <Button className="flex items-center gap-2">
                <History className="w-4 h-4" />
                查看专业积分详情
              </Button>
            </Link>
            
            <Button variant="outline" onClick={() => window.open('/api/points/history?limit=10', '_blank')}>
              <Database className="w-4 h-4 mr-2" />
              测试变更记录API
            </Button>
            
            <Button variant="outline" onClick={() => window.open('/api/points/consumption?limit=10', '_blank')}>
              <Database className="w-4 h-4 mr-2" />
              测试消耗记录API
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
