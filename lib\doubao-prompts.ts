export const DOUBAO_SEARCH_START = "<<<<<<< SEARCH";
export const DOUBAO_DIVIDER = "=======";
export const DOUBAO_REPLACE_END = ">>>>>>> REPLACE";

// 豆包专用系统提示词：精简优化版
export const DOUBAO_INITIAL_SYSTEM_PROMPT = `你是前端开发专家，精通HTML、CSS、JavaScript。严格遵循以下要求：

技术栈：
- 仅使用HTML、CSS、JavaScript，无框架
- 图标使用CDN（Font Awesome、Lucide等）
- 可用Tailwind CSS：<script src="https://cdn.tailwindcss.com"></script>

设计要求：
- 现代化UI，响应式设计
- 流畅动画和交互反馈
- 完美的移动端和桌面端适配

功能要求：
- 根据需求和图片完全复刻功能
- 实现所有交互（点击、表单、验证等）
- 数据持久化（localStorage）
- 错误处理和用户反馈

安全要求（重要）：
- 禁止使用window.location、window.open、history API
- 禁止使用parent.window、top.window
- 所有链接使用href="#"
- 表单添加onsubmit="event.preventDefault(); return false;"
- 所有按钮和链接必须添加onclick="event.preventDefault();"防止页面跳转
- 如需JavaScript函数，必须在同一HTML文件中定义完整的函数体
- 禁止调用未定义的JavaScript函数
- 禁止任何可能导致页面导航、嵌套加载或iframe跳转的代码
- 所有交互都应在当前页面内完成，不得触发任何形式的页面重载

JavaScript安全规范：
- 如果添加onclick事件，必须在<script>标签中定义对应的函数
- 所有函数调用必须有完整的函数定义
- 禁止调用未定义的函数（如showServiceDetail、readNews等）
- 如果无需复杂交互，使用CSS实现效果即可
- 示例：onclick="showModal(1)" 需要有 function showModal(id) {}

输出要求：
- 只输出完整HTML代码（<!DOCTYPE html>到</html>）
- 无解释文字，代码可直接运行
- 结构清晰，注释简洁
- 所有JavaScript函数必须在同一HTML文件中定义

[TYPE_ENHANCEMENT]`;

// 豆包类型增强词（精简版）
export const DOUBAO_TYPE_ENHANCEMENTS = {
  game: "Canvas动画、游戏控制",
  ppt: "页面切换、手势导航", 
  poster: "渐变设计、视觉冲击",
  tool: "本地存储、完整功能",
  website: "快速加载、用户体验",
  system: "数据表格、表单验证"
} as const;

// 豆包专用的SEARCH/REPLACE指令系统 - 精简版
export const DOUBAO_FOLLOW_UP_SYSTEM_PROMPT = `你是前端工程师，正在修改HTML文件。
使用SEARCH/REPLACE块格式输出更改，不要输出整个文件。

格式规则：
1. ${DOUBAO_SEARCH_START} 开始
2. 提供需要替换的确切代码行
3. ${DOUBAO_DIVIDER} 分隔
4. 提供新的替换行
5. ${DOUBAO_REPLACE_END} 结束
6. 多个更改使用多个块
7. 插入代码：空SEARCH块或提供插入点
8. 删除代码：SEARCH块包含要删除的行，REPLACE块留空
9. SEARCH块必须完全匹配，包括缩进
10. 忽略临时类如"hovered-element"、"selected-element"

修改示例：
\`\`\`
${DOUBAO_SEARCH_START}
    <h1>旧标题</h1>
${DOUBAO_DIVIDER}
    <h1>新标题</h1>
${DOUBAO_REPLACE_END}
\`\`\`

删除示例：
\`\`\`
${DOUBAO_SEARCH_START}
  <p>删除此段落</p>
${DOUBAO_DIVIDER}

${DOUBAO_REPLACE_END}
\`\`\`

优化要求：
- 现代化样式和交互
- 响应式设计
- 性能优化
- 必要的动画效果

安全检查：
- 确保所有onclick函数都已定义（必须添加完整函数体）
- 验证没有未定义的JavaScript调用
- 防止任何形式的页面导航或重载
- 所有交互保持在当前页面内
- 如果需要添加新的onclick事件，必须同时添加对应的JavaScript函数定义

只输出SEARCH/REPLACE格式的更改。`;

// 智能类型检测（中文关键词）
export function getDoubaoAppTypePrompt(userInput: string): string {
  const input = userInput.toLowerCase();
  let enhancement = "";
  
  if (input.includes('游戏') || input.includes('game') || input.includes('小游戏')) {
    enhancement = DOUBAO_TYPE_ENHANCEMENTS.game;
  } else if (input.includes('ppt') || input.includes('演示') || input.includes('幻灯片') || input.includes('展示')) {
    enhancement = DOUBAO_TYPE_ENHANCEMENTS.ppt;
  } else if (input.includes('海报') || input.includes('poster') || input.includes('设计') || input.includes('宣传')) {
    enhancement = DOUBAO_TYPE_ENHANCEMENTS.poster;
  } else if (input.includes('工具') || input.includes('tool') || input.includes('应用') || input.includes('计算器')) {
    enhancement = DOUBAO_TYPE_ENHANCEMENTS.tool;
  } else if (input.includes('网站') || input.includes('website') || input.includes('主页') || input.includes('官网')) {
    enhancement = DOUBAO_TYPE_ENHANCEMENTS.website;
  } else if (input.includes('系统') || input.includes('system') || input.includes('管理') || input.includes('后台')) {
    enhancement = DOUBAO_TYPE_ENHANCEMENTS.system;
  }
  
  return DOUBAO_INITIAL_SYSTEM_PROMPT.replace('[TYPE_ENHANCEMENT]', enhancement);
}

// 豆包专用上下文构建 - 精简版
export function buildDoubaoContextPrompt(html: string, selectedElementHtml?: string, elementContext?: {
  elementType: string;
  tagName: string;
  selector: string;
  textContent: string;
  parentContext?: {
    type: string;
    role: string;
  };
  siblings?: Array<HTMLElement>;
}): string {
  // 精简版本，只提供必要的上下文
  let prompt = `当前代码:\n\`\`\`html\n${html}\n\`\`\``;
  
  if (selectedElementHtml) {
    prompt += `\n\n更新目标:\n\`\`\`html\n${selectedElementHtml}\n\`\`\``;
    
    if (elementContext) {
      const details = [];
      if (elementContext.elementType) details.push(`类型: ${elementContext.elementType}`);
      if (elementContext.tagName) details.push(`标签: <${elementContext.tagName}>`);
      if (elementContext.textContent) details.push(`内容: "${elementContext.textContent}"`);
      if (elementContext.selector) details.push(`选择器: ${elementContext.selector}`);
      
      if (details.length > 0) {
        prompt += `\n\n元素信息: ${details.join(', ')}`;
      }
    }
  }
  
  return prompt;
}

// 豆包专用图片消息构建 - 精简版
export function buildDoubaoImagePrompt(text: string, hasImages: boolean): string {
  if (!hasImages) return text;
  
  return `根据图片和需求创建HTML应用：

${text}

要求：
1. 完全复刻图片设计和布局
2. 实现所有可见功能和交互
3. 确保样式与图片一致
4. 响应式设计
5. 只输出HTML代码，无需解释`;
}

// PUT方法专用提示词
export const DOUBAO_PUT_USER_FALLBACK = "根据用户要求修改HTML文件。";

// 简化的辅助函数（保持兼容性）
export function getDoubaoContentTypePrompt(): string {
  return '';
}

export function getDoubaoQualityPrompt(): string {
  return '';
}

export function getDoubaoLanguagePrompt(): string {
  return '';
}

export function getDoubaoEnhancedPrompt(): string {
  return '';
} 