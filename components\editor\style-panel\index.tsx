"use client";
import { useState, useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { X, Paintbrush, Type, Save } from "lucide-react";
import { QuickStylesPanel } from "./components/QuickStylesPanel";
import { TextEditor } from "./components/TextEditor";
import { StyleManager } from "./utils/StyleManager";
import { HTMLCodeIntegrator } from "./utils/HTMLCodeIntegrator";

interface StylePanelProps {
  selectedElement: HTMLElement | null;
  onClose: () => void;
  onApplyStyles: (styles: CSSStyleDeclaration) => void;
  onPreviewStyles: (styles: CSSStyleDeclaration) => void;
  onSave?: (htmlContent: string, versionNumber?: number) => Promise<void>; // 🎯 修改：支持传递版本号
  onSwitchToChat?: () => void; // 新增切换到聊天模式的回调
  onHtmlChange?: (newHtml: string) => void; // 新增HTML变化回调
  currentVersionNumber?: number; // 🎯 新增：当前预览的版本号
}

// StyleManager已经处理了样式属性定义，这里不再需要

export function StylePanel({
  selectedElement,
  onClose,
  onApplyStyles,
  onPreviewStyles,
  onSave,
  onSwitchToChat,
  onHtmlChange,
  currentVersionNumber
}: StylePanelProps) {
  const [activeTab, setActiveTab] = useState<'styles' | 'text'>('styles');
  const [styleValues, setStyleValues] = useState<Record<string, string>>({});

  // 使用StyleManager读取有效样式
  const readElementStyles = useCallback((element: HTMLElement) => {
    return StyleManager.getEffectiveStyles(element);
  }, []);

  // 初始化样式值
  useEffect(() => {
    if (selectedElement) {
      const initialValues = readElementStyles(selectedElement);
      setStyleValues(initialValues);
    } else {
      setStyleValues({});
    }
  }, [selectedElement, readElementStyles]);

  // 处理样式值变化 - 修复后的版本
  const handleStyleChange = useCallback((key: string, value: string) => {
    // 立即更新本地状态
    setStyleValues(prev => {
      // 如果值没有变化，不触发更新
      if (prev[key] === value) return prev;
      
      const newStyleValues = {
        ...prev,
        [key]: value
      };
      
      // 立即应用样式到元素（同时更新内联样式和CSS类）
      if (selectedElement) {
        // 1. 先设置内联样式用于即时预览
        selectedElement.style.setProperty(key, value);
        
        // 2. 同时应用到CSS类以确保持久化
        StyleManager.applyStylesToCSS(selectedElement, newStyleValues);
        
        // 3. 触发预览更新
        requestAnimationFrame(() => {
          onPreviewStyles(selectedElement.style);
        });
        
        // 4. 关键修复：实时同步HTML状态到编辑器（确保代码编辑器显示最新内容）
        requestAnimationFrame(() => {
          let currentHTML = HTMLCodeIntegrator.extractCurrentHTML();
          
          // 🔧 新增：实时清理Tailwind重复内容，防止样式污染
          currentHTML = StyleManager.realtimeCleanTailwind(currentHTML);
          
          if (onHtmlChange) {
            onHtmlChange(currentHTML);
            console.log(`🔄 HTML状态已实时同步并清理: ${key} = ${value}`, {
              element: selectedElement.tagName,
              htmlLength: currentHTML.length,
              reason: '样式修改后立即同步到编辑器并清理Tailwind重复内容'
            });
          }
        });
        
        console.log(`✅ 样式已应用: ${key} = ${value}`, {
          element: selectedElement.tagName,
          inlineStyle: selectedElement.style.getPropertyValue(key),
          customCSS: StyleManager.exportCustomCSS().length > 0
        });
      }
      
      return newStyleValues;
    });
  }, [selectedElement, onPreviewStyles]);



  // 重置样式 - 使用StyleManager清理
  const handleResetStyles = useCallback(() => {
    if (selectedElement) {
      // 使用StyleManager重置元素样式
      StyleManager.resetElementStyles(selectedElement);
      
      // 重新读取样式
      const resetValues = readElementStyles(selectedElement);
      setStyleValues(resetValues);
      
      console.log('Styles reset, remaining CSS:', StyleManager.exportCustomCSS());
      
      // 触发应用样式
      requestAnimationFrame(() => {
        onApplyStyles(selectedElement.style);
      });
    }
      }, [selectedElement, readElementStyles, onApplyStyles]);

  // 保存到数据库的处理函数
  const handleSaveToDatabase = useCallback(async () => {
    if (!selectedElement) return;
    
    try {
      console.log('🔄 开始保存样式修改到数据库...');
      
      // 1. 首先应用当前样式到元素并清理内联样式
      if (Object.keys(styleValues).length > 0) {
        StyleManager.applyStylesToCSS(selectedElement, styleValues);
        // 清理内联样式，只保留CSS类
        StyleManager.cleanInlineStyles(selectedElement, Object.keys(styleValues));
        console.log('✅ 样式已应用到CSS类，内联样式已清理');
      }
      
      // 2. 提取完整的HTML内容（包含样式修改）
      let currentHTML = HTMLCodeIntegrator.extractCurrentHTML();
      console.log('🔄 HTML内容已提取', { htmlLength: currentHTML.length });
      
      // 🔧 新增：最终保存前进行彻底的Tailwind清理
      currentHTML = StyleManager.cleanTailwindDuplicates(currentHTML);
      console.log('🧹 HTML内容已清理Tailwind重复内容', { htmlLength: currentHTML.length });
      
      // 3. 关键修复：同步HTML状态到编辑器
      if (onHtmlChange) {
        onHtmlChange(currentHTML);
        console.log('🔄 HTML状态已同步到编辑器');
      }
      
      // 4. 保存到数据库
      if (onSave) {
        console.log('💾 开始保存到数据库...', {
          currentVersionNumber,
          htmlLength: currentHTML.length
        });
        await onSave(currentHTML, currentVersionNumber);
        console.log('✅ 样式修改已成功保存到数据库');
        
        // 5. 保存成功后切换到聊天模式
        if (onSwitchToChat) {
          console.log('🔄 切换到聊天模式...');
          onSwitchToChat();
        }
        
        // 6. 关闭样式面板
        onClose();
      }
    } catch (error) {
      console.error('❌ 保存样式时发生错误:', error);
      alert('保存失败，请重试');
    }
  }, [selectedElement, styleValues, onSave, onSwitchToChat, onClose, onHtmlChange, currentVersionNumber]);

  if (!selectedElement) {
    return null;
  }

  return (
    <div className="h-full bg-background border-r border-border flex flex-col shadow-lg">
      {/* 头部信息栏 - 浅色模式优化 */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-border bg-card/50">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div className="w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center border border-primary/20">
            <Paintbrush className="w-4 h-4 text-primary" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-semibold text-foreground truncate">
              {(() => {
                const tagName = selectedElement.tagName.toLowerCase();
                const textContent = selectedElement.textContent?.trim();
                
                if (tagName === 'button' && textContent) {
                  return `按钮（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）`;
                }
                if (textContent && textContent.length > 0) {
                  return `文本（${textContent.substring(0, 10)}${textContent.length > 10 ? '...' : ''}）`;
                }
                return `${tagName.toUpperCase()} 元素`;
              })()}
            </div>
            <div className="text-xs text-muted-foreground">
              样式编辑器
            </div>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* 标签页切换 - 浅色模式优化 */}
      <div className="flex bg-muted/30 border-b border-border">
        {[
          { key: 'styles', label: '快捷样式', icon: Paintbrush },
          { key: 'text', label: '文本编辑', icon: Type }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as 'styles' | 'text')}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 text-sm font-medium transition-all duration-200 ${
              activeTab === key
                ? 'text-primary bg-background border-b-2 border-primary shadow-sm'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span>{label}</span>
          </button>
        ))}
      </div>

      {/* 内容面板 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          {activeTab === 'styles' ? (
            <QuickStylesPanel
              selectedElement={selectedElement}
              styleValues={styleValues}
              onStyleChange={handleStyleChange}
            />
          ) : (
            <TextEditor
              selectedElement={selectedElement}
              onPreviewStyles={onPreviewStyles}
            />
          )}
        </div>
      </div>

      {/* 底部操作栏 - 修改后的版本 */}
      <div className="p-4 border-t border-border bg-card/50">
        <div className="flex space-x-2">
          <Button
            onClick={handleSaveToDatabase}
            className="flex-1 h-9 bg-slate-700 hover:bg-slate-800 text-white text-sm font-medium transition-all duration-200 shadow-sm"
          >
            <Save className="w-4 h-4 mr-2" />
            保存
          </Button>
          <Button
            onClick={handleResetStyles}
            variant="outline"
            className="flex-1 h-9 border-border text-muted-foreground hover:bg-muted hover:text-foreground text-sm font-medium transition-all duration-200"
          >
            重置
          </Button>
        </div>
      </div>
    </div>
  );
} 