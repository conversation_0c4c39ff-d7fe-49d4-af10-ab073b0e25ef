"use client";

import { useState } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Header } from "@/components/editor/header";
import { 
  Database, 
  User, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  History,
  Bug,
  Zap,
  Code
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface TestResult {
  name: string;
  status: 'success' | 'error';
  query?: string;
  result?: number;
  data?: any;
  error?: string;
  message: string;
}

export default function TestSQLFixPage() {
  const { user } = useUser();
  const router = useRouter();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [historyTest, setHistoryTest] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // 测试SQL查询
  const testSQL = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/points/test-sql');
      const data = await response.json();
      if (data.success) {
        setTestResults(data.data.testResults);
        toast.success(`SQL测试完成: ${data.data.summary.successCount}/${data.data.summary.totalTests} 成功`);
      } else {
        toast.error('SQL测试失败');
      }
    } catch (error) {
      console.error('SQL测试失败:', error);
      toast.error('SQL测试失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试修复后的历史记录API
  const testHistoryAPI = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/points/history?limit=10');
      const data = await response.json();
      setHistoryTest(data);
      if (data.success) {
        toast.success(`历史记录API测试成功: ${data.data.history.length} 条记录`);
      } else {
        toast.error(`历史记录API测试失败: ${data.error}`);
      }
    } catch (error) {
      console.error('历史记录API测试失败:', error);
      toast.error('历史记录API测试失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: 'success' | 'error') => {
    return status === 'success' ? 
      <CheckCircle className="w-4 h-4 text-green-600" /> : 
      <AlertCircle className="w-4 h-4 text-red-600" />;
  };

  const getStatusColor = (status: 'success' | 'error') => {
    return status === 'success' ? 
      'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
      'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
  };

  return (
    <div className="h-screen bg-background flex flex-col">
      <Header onLogoClick={() => router.push('/')} />
      
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          <div className="flex items-center gap-2 mb-6">
            <Bug className="w-6 h-6" />
            <h1 className="text-2xl font-bold">SQL 列名歧义修复验证</h1>
          </div>

          {/* 问题说明 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                问题根源分析
              </CardTitle>
              <CardDescription>Column 'user_id' in where clause is ambiguous</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-red-600">问题原因</h4>
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                    <code className="text-sm">
                      {`FROM points_transactions pt
LEFT JOIN user_points_balance upb ON ...
WHERE user_id = ?  -- ❌ 歧义！两个表都有 user_id`}
                    </code>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    在联表查询中，两个表都有 user_id 字段，MySQL 无法确定使用哪个表的字段。
                  </p>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-600">修复方案</h4>
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <code className="text-sm">
                      {`FROM points_transactions pt
LEFT JOIN user_points_balance upb ON ...
WHERE pt.user_id = ?  -- ✅ 明确指定表别名`}
                    </code>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    使用表别名 pt.user_id 明确指定使用 points_transactions 表的 user_id 字段。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 用户状态 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                当前用户状态
              </CardTitle>
            </CardHeader>
            <CardContent>
              {user ? (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{user.id}</div>
                    <div className="text-sm text-muted-foreground">用户ID</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{user.points || 0}</div>
                    <div className="text-sm text-muted-foreground">当前积分</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="text-lg font-bold text-purple-600">{user.total_earned_points || 0}</div>
                    <div className="text-sm text-muted-foreground">累计获得</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <div className="text-lg font-bold text-orange-600">{user.total_spent_points || 0}</div>
                    <div className="text-sm text-muted-foreground">累计消费</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">请先登录以进行SQL测试</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 测试按钮 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <Button 
              onClick={testSQL}
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
              <Code className="w-4 h-4" />
              测试SQL查询
            </Button>
            
            <Button 
              onClick={testHistoryAPI}
              disabled={loading}
              variant="outline"
              className="flex items-center gap-2"
            >
              {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
              <History className="w-4 h-4" />
              测试历史记录API
            </Button>
          </div>

          {/* SQL测试结果 */}
          {testResults.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  SQL查询测试结果
                </CardTitle>
                <CardDescription>各种SQL查询的执行状态</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {testResults.map((result, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          {getStatusIcon(result.status)}
                          <div>
                            <div className="font-medium">{result.name}</div>
                            <div className="text-sm text-muted-foreground">{result.message}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(result.status)} variant="secondary">
                            {result.status}
                          </Badge>
                          {result.result !== undefined && (
                            <Badge variant="outline">
                              {result.result} 条
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      {result.query && (
                        <div className="mt-3">
                          <div className="text-xs font-medium text-muted-foreground mb-1">SQL查询:</div>
                          <div className="bg-muted/50 p-2 rounded text-xs font-mono overflow-x-auto">
                            {result.query.trim()}
                          </div>
                        </div>
                      )}
                      
                      {result.error && (
                        <div className="mt-3">
                          <div className="text-xs font-medium text-red-600 mb-1">错误信息:</div>
                          <div className="bg-red-50 dark:bg-red-900/20 p-2 rounded text-xs text-red-700 dark:text-red-400">
                            {result.error}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* API测试结果 */}
          {historyTest && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="w-5 h-5" />
                  历史记录API测试结果
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>API状态:</span>
                    <Badge className={historyTest.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {historyTest.success ? '成功' : '失败'}
                    </Badge>
                  </div>
                  
                  {historyTest.success ? (
                    <>
                      <div className="flex items-center justify-between">
                        <span>返回记录数:</span>
                        <span>{historyTest.data.history.length}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>总记录数:</span>
                        <span>{historyTest.data.pagination.total}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>分页信息:</span>
                        <span>第 {historyTest.data.pagination.currentPage} 页，共 {historyTest.data.pagination.totalPages} 页</span>
                      </div>
                    </>
                  ) : (
                    <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded">
                      <div className="text-sm text-red-700 dark:text-red-400">
                        错误: {historyTest.error}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 修复说明 */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-yellow-600" />
                修复详情
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                  <span className="text-sm">所有 WHERE 条件都使用了表别名 pt.user_id</span>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                  <span className="text-sm">所有筛选条件都使用了正确的表前缀</span>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                  <span className="text-sm">COUNT 查询也使用了表别名避免歧义</span>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                  <span className="text-sm">保持了原有的查询逻辑和性能</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
