# 阿里云短信服务配置指南

## 1. 获取阿里云访问凭证

### 步骤1：登录阿里云控制台
1. 访问 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 登录您的阿里云账号

### 步骤2：创建AccessKey
1. 点击右上角头像，选择 "AccessKey管理"
2. 点击 "创建AccessKey"
3. 记录下 `AccessKey ID` 和 `AccessKey Secret`

## 2. 配置短信服务

### 步骤1：开通短信服务
1. 在阿里云控制台搜索 "短信服务"
2. 开通短信服务并完成实名认证

### 步骤2：申请签名
1. 在短信服务控制台，点击 "签名管理"
2. 添加签名，例如：`万来云边`
3. 等待审核通过

### 步骤3：申请模板
1. 在短信服务控制台，点击 "模板管理"
2. 添加验证码模板，例如：
   - 模板内容：`您的验证码是${code}，5分钟内有效。`
   - 模板类型：验证码
3. 记录模板CODE，例如：`SMS_324480375`

## 3. 配置环境变量

在项目根目录的 `.env` 文件中添加以下配置：

```env
# 阿里云短信服务配置
ALIYUN_ACCESS_KEY_ID=your_access_key_id_here
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret_here
ALIYUN_SMS_SIGN_NAME=万来云边
ALIYUN_SMS_TEMPLATE_CODE=SMS_324480375
```

**重要提醒：**
- 请将 `your_access_key_id_here` 替换为您的实际 AccessKey ID
- 请将 `your_access_key_secret_here` 替换为您的实际 AccessKey Secret
- 签名名称和模板CODE请根据您在阿里云申请的实际值填写

## 4. 测试短信发送

配置完成后，您可以通过以下方式测试：

1. 启动项目：`npm run dev`
2. 访问登录页面
3. 输入手机号并点击"发送验证码"
4. 检查手机是否收到验证码

## 5. 安全建议

1. **不要将AccessKey提交到代码仓库**
2. **定期轮换AccessKey**
3. **为AccessKey设置最小权限**（仅短信服务权限）
4. **在生产环境使用RAM子账号**

## 6. 费用说明

- 国内短信：约 0.045元/条
- 建议设置短信发送频率限制，避免恶意刷取
- 可在阿里云控制台设置费用预警

## 7. 常见问题

### Q: 短信发送失败怎么办？
A: 检查以下几点：
1. AccessKey是否正确
2. 签名和模板是否审核通过
3. 手机号格式是否正确
4. 账户余额是否充足

### Q: 如何查看短信发送记录？
A: 在阿里云短信服务控制台的"发送记录"中可以查看详细的发送状态

### Q: 验证码收不到怎么办？
A: 
1. 检查手机号是否正确
2. 查看短信是否被拦截
3. 检查阿里云控制台的发送记录
4. 确认模板内容是否符合规范
