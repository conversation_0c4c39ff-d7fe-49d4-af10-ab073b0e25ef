"use client"

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Monitor, Sun, Moon, ArrowRight, Sparkles, Code, Zap, Users, Rocket, ChevronDown, ChevronRight, Play, Settings, Image, MessageCircle, Edit, History, Download, Crown, HelpCircle, Menu, X, ChevronLeft } from 'lucide-react'
import { cn } from '@/lib/utils'
import { GhostLogo } from '@/components/ui/ghost-logo'
import { useThemeLanguage } from '@/components/providers/theme-language-provider'

interface MenuItem {
  id: string
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  type: 'page' | 'dropdown'
  children?: MenuItem[]
  highlights?: string[]
  benefits?: string[]
}

const menuItems: MenuItem[] = [
  {
    id: 'introduction',
    title: 'LoomRun 介绍',
    description: 'AI 驱动的革命性网站构建平台，让创意瞬间成为现实',
    icon: Sparkles,
    type: 'page',
    highlights: [
      '基于 DeepSeek AI 的强大引擎',
      '支持自然语言对话创建网站',
      '零编程基础即可使用',
      '现代化的技术架构'
    ],
    benefits: [
      '降低网站开发门槛',
      '大幅提升开发效率',
      '释放创意潜能'
    ]
  },
  {
    id: 'quick-start',
    title: '快速开始',
    description: '5分钟快速上手，从零到发布您的第一个网站',
    icon: Play,
    type: 'page',
    highlights: [
      '简单三步即可开始',
      '无需复杂配置',
      '实时指导和提示',
      '丰富的模板库'
    ],
    benefits: [
      '快速上手体验',
      '降低学习成本',
      '即时看到效果'
    ]
  },
  {
    id: 'core-features',
    title: '核心功能',
    description: '强大的功能集合，满足各种网站开发需求',
    icon: Settings,
    type: 'dropdown',
    children: [
      {
        id: 'natural-language',
        title: '自然语言生成界面',
        description: '通过对话方式描述需求，AI 自动生成专业界面',
        icon: MessageCircle,
        type: 'page',
        highlights: [
          '支持中文、英文自然语言',
          '智能理解复杂需求',
          '实时流式生成效果',
          '上下文记忆对话'
        ],
        benefits: [
          '无需学习代码语法',
          '表达更加自然直观',
          '快速实现创意想法'
        ]
      },
      {
        id: 'image-clone',
        title: '上传图片复刻页面',
        description: '上传任意网页截图，AI 智能识别并重建页面结构',
        icon: Image,
        type: 'page',
        highlights: [
          '支持多种图片格式',
          '智能识别页面布局',
          '自动提取设计元素',
          '保持原有视觉风格'
        ],
        benefits: [
          '快速复制优秀设计',
          '节省设计时间',
          '学习最佳实践'
        ]
      },
      {
        id: 'interactive-chat',
        title: '继续对话实现交互',
        description: '通过持续对话不断完善和优化您的网站',
        icon: MessageCircle,
        type: 'page',
        highlights: [
          '支持多轮对话优化',
          '智能记忆修改历史',
          '实时预览修改效果',
          '支持细节调整'
        ],
        benefits: [
          '迭代式完善设计',
          '精确控制每个细节',
          '提升最终效果'
        ]
      },
      {
        id: 'smart-edit',
        title: '智能辅助编辑页面',
        description: 'AI 智能提示和辅助，让编辑更加高效准确',
        icon: Edit,
        type: 'page',
        highlights: [
          '智能代码补全',
          '实时错误检测',
          '样式建议推荐',
          '响应式设计辅助'
        ],
        benefits: [
          '减少编辑错误',
          '提升编辑效率',
          '学习最佳实践'
        ]
      },
      {
        id: 'manual-edit',
        title: '灵活手动编辑页面',
        description: '完全的代码控制权，支持高级用户的精细化编辑',
        icon: Code,
        type: 'page',
        highlights: [
          '完整的 HTML/CSS/JS 编辑',
          '实时预览和调试',
          '语法高亮和格式化',
          '代码折叠和搜索'
        ],
        benefits: [
          '满足高级需求',
          '完全的控制权',
          '专业开发体验'
        ]
      },
      {
        id: 'version-history',
        title: '灵活查看历史版本',
        description: '完整的版本管理系统，随时回溯和比较不同版本',
        icon: History,
        type: 'page',
        highlights: [
          '自动保存每次修改',
          '可视化版本对比',
          '一键回滚到任意版本',
          '分支管理功能'
        ],
        benefits: [
          '避免意外丢失',
          '安全的实验环境',
          '团队协作支持'
        ]
      },
      {
        id: 'community-resources',
        title: '社区资源随便使用',
        description: '丰富的社区模板和组件库，一键导入使用',
        icon: Users,
        type: 'page',
        highlights: [
          '精选社区作品展示',
          '一键导入和修改',
          '分类标签系统',
          '用户评分和评论'
        ],
        benefits: [
          '快速获得灵感',
          '节省开发时间',
          '学习优秀案例'
        ]
      },
      {
        id: 'one-click-deploy',
        title: '一键部署分享应用',
        description: '集成多种部署方案，瞬间将作品发布到全球',
        icon: Rocket,
        type: 'page',
        highlights: [
          '支持多种部署平台',
          '自动生成访问链接',
          '全球 CDN 加速',
          'HTTPS 安全保障'
        ],
        benefits: [
          '快速上线发布',
          '全球访问优化',
          '专业部署体验'
        ]
      },
      {
        id: 'export-formats',
        title: '支持导出多种格式',
        description: '灵活的导出选项，支持各种主流格式和平台',
        icon: Download,
        type: 'page',
        highlights: [
          '支持 HTML/CSS/JS 导出',
          '静态网站包下载',
          '源码压缩包',
          '移动端适配版本'
        ],
        benefits: [
          '灵活的使用方式',
          '便于二次开发',
          '多平台兼容'
        ]
      },
      {
        id: 'membership',
        title: '会员订阅积分服务',
        description: '灵活的会员体系，享受更多高级功能和服务',
        icon: Crown,
        type: 'page',
        highlights: [
          '多层级会员体系',
          '积分奖励机制',
          '高级功能解锁',
          '优先技术支持'
        ],
        benefits: [
          '更多功能权限',
          '优质服务体验',
          '持续价值回报'
        ]
      }
    ]
  },
  {
    id: 'usage-tips',
    title: '使用技巧',
    description: '实用的使用技巧和最佳实践，让您更高效地使用 LoomRun',
    icon: HelpCircle,
    type: 'dropdown',
    children: [
      {
        id: 'community-usage',
        title: '如何使用社区资源',
        description: '充分利用社区资源，快速获得设计灵感和解决方案',
        icon: Users,
        type: 'page',
        highlights: [
          '浏览和筛选优质作品',
          '一键导入并自定义修改',
          '学习他人的设计思路',
          '参与社区互动交流'
        ],
        benefits: [
          '快速获得灵感',
          '学习最佳实践',
          '节省开发时间'
        ]
      },
      {
        id: 'element-editing',
        title: '如何编辑页面元素',
        description: '掌握页面元素编辑的各种技巧和方法',
        icon: Edit,
        type: 'page',
        highlights: [
          '选择和定位元素',
          '修改样式和属性',
          '添加交互效果',
          '响应式设计调整'
        ],
        benefits: [
          '精确控制页面效果',
          '提升编辑效率',
          '实现专业设计'
        ]
      },
      {
        id: 'ai-prompt-tips',
        title: '如何优化AI对话提示',
        description: '掌握与AI对话的技巧，获得更好的生成效果',
        icon: MessageCircle,
        type: 'page',
        highlights: [
          '清晰描述需求和目标',
          '提供具体的样式要求',
          '使用参考案例说明',
          '分步骤逐步完善'
        ],
        benefits: [
          '提高生成质量',
          '减少修改次数',
          '节省开发时间'
        ]
      },
      {
        id: 'responsive-design',
        title: '如何实现响应式设计',
        description: '创建适配各种设备的响应式网页设计',
        icon: Monitor,
        type: 'page',
        highlights: [
          '移动端优先设计原则',
          '断点设置和媒体查询',
          '弹性布局和网格系统',
          '图片和内容自适应'
        ],
        benefits: [
          '提升用户体验',
          '扩大受众覆盖',
          '符合现代标准'
        ]
      },
      {
        id: 'performance-optimization',
        title: '如何优化网站性能',
        description: '提升网站加载速度和用户体验的实用技巧',
        icon: Zap,
        type: 'page',
        highlights: [
          '图片压缩和格式优化',
          '代码精简和压缩',
          'CDN加速配置',
          '缓存策略设置'
        ],
        benefits: [
          '提升加载速度',
          '改善用户体验',
          '提高搜索排名'
        ]
      },
      {
        id: 'deployment-tips',
        title: '如何选择部署方案',
        description: '根据项目需求选择最适合的部署平台和配置',
        icon: Rocket,
        type: 'page',
        highlights: [
          '免费vs付费平台对比',
          '域名绑定和SSL配置',
          '自动化部署流程',
          '监控和维护策略'
        ],
        benefits: [
          '降低部署成本',
          '提高部署效率',
          '确保服务稳定'
        ]
      }
    ]
  }
]

export default function FeaturesPage() {
  const { resolvedTheme, changeTheme } = useThemeLanguage()
  const [selectedItem, setSelectedItem] = useState<string>('introduction')
  const [expandedDropdowns, setExpandedDropdowns] = useState<string[]>(['core-features'])
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const toggleTheme = () => {
    const newTheme = resolvedTheme === 'light' ? 'dark' : 'light'
    changeTheme(newTheme)
  }

  const handleStartFree = () => {
    window.location.href = '/projects/new'
  }

  const toggleDropdown = (id: string) => {
    setExpandedDropdowns(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  const getCurrentItem = (): MenuItem => {
    // 首先在顶级菜单中查找
    const topLevel = menuItems.find(item => item.id === selectedItem)
    if (topLevel) return topLevel

    // 然后在子菜单中查找
    for (const item of menuItems) {
      if (item.children) {
        const child = item.children.find(child => child.id === selectedItem)
        if (child) return child
      }
    }

    return menuItems[0] // 默认返回第一个
  }

  const currentItem = getCurrentItem()

  // 获取所有页面的扁平列表（用于导航）
  const getAllPages = () => {
    const pages: MenuItem[] = []
    menuItems.forEach(item => {
      if (item.type === 'page') {
        pages.push(item)
      } else if (item.children) {
        pages.push(...item.children)
      }
    })
    return pages
  }

  const allPages = getAllPages()
  const currentIndex = allPages.findIndex(page => page.id === selectedItem)
  const prevPage = currentIndex > 0 ? allPages[currentIndex - 1] : null
  const nextPage = currentIndex < allPages.length - 1 ? allPages[currentIndex + 1] : null

  return (
    <div className={cn("min-h-screen transition-colors duration-300", resolvedTheme)}>
      <div className="bg-background text-foreground">
        {/* Header */}
        <header className="border-b border-border bg-background/80 backdrop-blur-sm sticky top-0 z-50">
          <div className="px-4 h-16 flex items-center justify-between">
            <button
              onClick={() => window.location.href = '/projects/new'}
              className="flex items-center gap-3 hover:opacity-80 transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500/50 rounded-lg p-1"
              title="返回首页"
            >
              <div className="w-8 h-8 rounded-lg flex items-center justify-center hover:scale-110 transition-all duration-300">
                <GhostLogo size={32} className="w-full h-full" />
              </div>
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 dark:from-blue-400 dark:via-purple-400 dark:to-blue-600 bg-clip-text text-transparent font-bold text-xl tracking-tight transition-all duration-300 hover:from-blue-700 hover:via-purple-700 hover:to-blue-900 dark:hover:from-blue-300 dark:hover:via-purple-300 dark:hover:to-blue-500 drop-shadow-sm">LoomRun</span>
            </button>

            <div className="flex items-center gap-2">
              {/* 移动端菜单按钮 */}
              <Button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                variant="ghost"
                size="icon"
                className="w-9 h-9 md:hidden"
              >
                {isMobileMenuOpen ? (
                  <X className="w-4 h-4" />
                ) : (
                  <Menu className="w-4 h-4" />
                )}
              </Button>

              <Button
                onClick={toggleTheme}
                variant="ghost"
                size="icon"
                className="w-9 h-9"
              >
                {resolvedTheme === 'light' ? (
                  <Moon className="w-4 h-4" />
                ) : (
                  <Sun className="w-4 h-4" />
                )}
              </Button>

              <Button onClick={handleStartFree} className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 hidden sm:flex">
                免费开始使用
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>

              <Button onClick={handleStartFree} size="sm" className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 sm:hidden">
                开始使用
              </Button>
            </div>
          </div>
        </header>

        {/* 移动端侧边栏覆盖层 */}
        {isMobileMenuOpen && (
          <div className="fixed inset-0 z-50 md:hidden">
            <div className="absolute inset-0 bg-black/50" onClick={() => setIsMobileMenuOpen(false)} />
            <aside className="absolute left-0 top-0 h-full w-64 bg-background border-r border-border p-4 overflow-y-auto">
              <div className="space-y-1">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-sm font-semibold">功能导航</h2>
                  <Button
                    onClick={() => setIsMobileMenuOpen(false)}
                    variant="ghost"
                    size="icon"
                    className="w-6 h-6"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
                {menuItems.map((item) => {
                  const Icon = item.icon
                  const isExpanded = expandedDropdowns.includes(item.id)

                  return (
                    <div key={item.id} className="space-y-1">
                      {/* 主菜单项 */}
                      <button
                        onClick={() => {
                          if (item.type === 'dropdown') {
                            toggleDropdown(item.id)
                          } else {
                            setSelectedItem(item.id)
                            setIsMobileMenuOpen(false)
                          }
                        }}
                        className={cn(
                          "w-full text-left p-2 rounded-md transition-colors text-sm",
                          selectedItem === item.id && item.type === 'page'
                            ? "bg-primary text-primary-foreground"
                            : "hover:bg-accent hover:text-accent-foreground"
                        )}
                      >
                        <div className="flex items-center gap-2">
                          <Icon className="w-4 h-4" />
                          <span className="flex-1">{item.title}</span>
                          {item.type === 'dropdown' && (
                            isExpanded ? (
                              <ChevronDown className="w-3 h-3" />
                            ) : (
                              <ChevronRight className="w-3 h-3" />
                            )
                          )}
                        </div>
                      </button>

                      {/* 子菜单项 */}
                      {item.type === 'dropdown' && isExpanded && item.children && (
                        <div className="ml-6 space-y-1">
                          {item.children.map((child) => {
                            const ChildIcon = child.icon
                            return (
                              <button
                                key={child.id}
                                onClick={() => {
                                  setSelectedItem(child.id)
                                  setIsMobileMenuOpen(false)
                                }}
                                className={cn(
                                  "w-full text-left p-2 rounded-md transition-colors text-sm",
                                  selectedItem === child.id
                                    ? "bg-primary text-primary-foreground"
                                    : "hover:bg-accent hover:text-accent-foreground"
                                )}
                              >
                                <div className="flex items-center gap-2">
                                  <ChildIcon className="w-3 h-3" />
                                  <span className="text-xs">{child.title}</span>
                                </div>
                              </button>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </aside>
          </div>
        )}

        {/* Main Content */}
        <div className="flex h-[calc(100vh-4rem)]">
          {/* Desktop Sidebar */}
          <aside className="w-64 border-r border-border bg-muted/30 p-4 overflow-y-auto hidden md:block">
            <div className="space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon
                const isExpanded = expandedDropdowns.includes(item.id)

                return (
                  <div key={item.id} className="space-y-1">
                    {/* 主菜单项 */}
                    <button
                      onClick={() => {
                        if (item.type === 'dropdown') {
                          toggleDropdown(item.id)
                        } else {
                          setSelectedItem(item.id)
                        }
                      }}
                      className={cn(
                        "w-full text-left p-2 rounded-md transition-colors text-sm",
                        selectedItem === item.id && item.type === 'page'
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-accent hover:text-accent-foreground"
                      )}
                    >
                      <div className="flex items-center gap-2">
                        <Icon className="w-4 h-4" />
                        <span className="flex-1">{item.title}</span>
                        {item.type === 'dropdown' && (
                          isExpanded ? (
                            <ChevronDown className="w-3 h-3" />
                          ) : (
                            <ChevronRight className="w-3 h-3" />
                          )
                        )}
                      </div>
                    </button>

                    {/* 子菜单项 */}
                    {item.type === 'dropdown' && isExpanded && item.children && (
                      <div className="ml-6 space-y-1">
                        {item.children.map((child) => {
                          const ChildIcon = child.icon
                          return (
                            <button
                              key={child.id}
                              onClick={() => setSelectedItem(child.id)}
                              className={cn(
                                "w-full text-left p-2 rounded-md transition-colors text-sm",
                                selectedItem === child.id
                                  ? "bg-primary text-primary-foreground"
                                  : "hover:bg-accent hover:text-accent-foreground"
                              )}
                            >
                              <div className="flex items-center gap-2">
                                <ChildIcon className="w-3 h-3" />
                                <span className="text-xs">{child.title}</span>
                              </div>
                            </button>
                          )
                        })}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </aside>

          {/* Content Area */}
          <main className="flex-1 overflow-y-auto">
            <div className="h-full flex flex-col">
              <div className="flex-1 p-8">
                {/* Simple Header */}
                <div className="mb-8">
                  <h1 className="text-3xl font-bold mb-3">{currentItem.title}</h1>
                  <p className="text-lg text-muted-foreground mb-6">{currentItem.description}</p>
                  <Button onClick={handleStartFree} size="lg" className="bg-blue-600 hover:bg-blue-700">
                    免费体验
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>

                {/* Markdown-style Content */}
                <div className="prose prose-gray dark:prose-invert max-w-none">
                  {currentItem.highlights && (
                    <div className="mb-8">
                      <h3 className="text-xl font-semibold mb-4">核心特性</h3>
                      <ul className="space-y-3">
                        {currentItem.highlights.map((highlight, index) => (
                          <li key={index} className="text-base leading-relaxed">{highlight}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {currentItem.benefits && (
                    <div className="mb-8">
                      <h3 className="text-xl font-semibold mb-4">核心优势</h3>
                      <ul className="space-y-3">
                        {currentItem.benefits.map((benefit, index) => (
                          <li key={index} className="text-base leading-relaxed">{benefit}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* 特殊内容：快速开始指南 */}
                  {currentItem.id === 'quick-start' && (
                    <div className="mb-8">
                      <h3 className="text-xl font-semibold mb-4">快速开始步骤</h3>
                      <ol className="space-y-4">
                        <li className="text-base leading-relaxed"><strong>访问 LoomRun</strong> - 打开浏览器访问 LoomRun 平台</li>
                        <li className="text-base leading-relaxed"><strong>描述需求</strong> - 用自然语言描述您想要创建的网站</li>
                        <li className="text-base leading-relaxed"><strong>实时预览</strong> - AI 生成代码，实时查看效果</li>
                        <li className="text-base leading-relaxed"><strong>调整优化</strong> - 通过对话继续完善您的网站</li>
                        <li className="text-base leading-relaxed"><strong>一键部署</strong> - 满意后一键部署到全球 CDN</li>
                      </ol>
                    </div>
                  )}

                  {/* 特殊内容：LoomRun 介绍 */}
                  {currentItem.id === 'introduction' && (
                    <div>
                      <div className="mb-8">
                        <h3 className="text-xl font-semibold mb-4">技术架构</h3>
                        <ul className="space-y-3">
                          <li className="text-base leading-relaxed"><strong>前端框架:</strong> Next.js 15 + React 19 + TypeScript</li>
                          <li className="text-base leading-relaxed"><strong>样式系统:</strong> Tailwind CSS + Shadcn/ui 组件库</li>
                          <li className="text-base leading-relaxed"><strong>AI 引擎:</strong> DeepSeek API 智能代码生成</li>
                          <li className="text-base leading-relaxed"><strong>数据库:</strong> MySQL 优化查询</li>
                          <li className="text-base leading-relaxed"><strong>身份验证:</strong> 自定义安全认证系统</li>
                          <li className="text-base leading-relaxed"><strong>部署优化:</strong> 适配现代托管平台</li>
                        </ul>
                      </div>

                      <div className="mb-8">
                        <h3 className="text-xl font-semibold mb-4">核心理念</h3>
                        <blockquote className="border-l-4 border-blue-500 pl-6 py-4 italic text-lg">
                          "编织逻辑，运行万物 - 让每个人都能用自然语言创造数字世界"
                        </blockquote>
                        <p className="text-base text-muted-foreground mt-4 leading-relaxed">
                          LoomRun 致力于打破编程与创意之间的壁垒，让技术服务于想象力
                        </p>
                      </div>
                    </div>
                  )}

                </div>

                {/* Professional CTA with Navigation */}
                <div className="mt-12 p-8 border border-border rounded-xl bg-gradient-to-br from-background to-muted/50 shadow-sm">
                  {/* CTA Content */}
                  <div className="text-center mb-8">
                    <h3 className="text-xl font-semibold mb-3">准备开始您的创作之旅？</h3>
                    <p className="text-base text-muted-foreground mb-6 leading-relaxed max-w-2xl mx-auto">
                      加入数千名开发者，用 LoomRun 将您的创意变为现实
                    </p>
                    <Button onClick={handleStartFree} size="lg" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-md">
                      立即免费开始
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>

                  {/* Navigation */}
                  <div className="border-t border-border pt-6">
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        {prevPage ? (
                          <Button
                            variant="ghost"
                            onClick={() => setSelectedItem(prevPage.id)}
                            className="flex items-center gap-3 text-muted-foreground hover:text-foreground hover:bg-muted/50 p-3 rounded-lg transition-all"
                          >
                            <ChevronLeft className="w-4 h-4" />
                            <div className="text-left">
                              <div className="text-xs text-muted-foreground mb-1">上一页</div>
                              <div className="text-sm font-medium">{prevPage.title}</div>
                            </div>
                          </Button>
                        ) : (
                          <div></div>
                        )}
                      </div>

                      <div className="flex-1 text-right">
                        {nextPage ? (
                          <Button
                            variant="ghost"
                            onClick={() => setSelectedItem(nextPage.id)}
                            className="flex items-center gap-3 text-muted-foreground hover:text-foreground hover:bg-muted/50 p-3 rounded-lg transition-all ml-auto"
                          >
                            <div className="text-right">
                              <div className="text-xs text-muted-foreground mb-1">下一页</div>
                              <div className="text-sm font-medium">{nextPage.title}</div>
                            </div>
                            <ArrowRight className="w-4 h-4" />
                          </Button>
                        ) : (
                          <div></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
