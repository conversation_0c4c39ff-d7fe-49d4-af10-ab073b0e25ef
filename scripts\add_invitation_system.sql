ALTER TABLE users ADD COLUMN invite_code varchar(20) UNIQUE DEFAULT NULL;
ALTER TABLE users ADD COLUMN invited_by_user_id int DEFAULT NULL;
ALTER TABLE users ADD COLUMN invitation_count int NOT NULL DEFAULT 0;

ALTER TABLE users ADD INDEX idx_invite_code (invite_code);
ALTER TABLE users ADD INDEX idx_invited_by (invited_by_user_id);
ALTER TABLE users ADD INDEX idx_invitation_count (invitation_count);

ALTER TABLE users ADD CONSTRAINT fk_users_invited_by FOREIGN KEY (invited_by_user_id) REFERENCES users (id) ON DELETE SET NULL;

CREATE TABLE user_invitations (
  id int NOT NULL AUTO_INCREMENT,
  inviter_user_id int NOT NULL,
  invited_user_id int NOT NULL,
  invite_code varchar(20) NOT NULL,
  invitation_status enum('pending','registered','expired') NOT NULL DEFAULT 'pending',
  points_awarded int NOT NULL DEFAULT 0,
  registered_at datetime DEFAULT NULL,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY unique_invited_user (invited_user_id),
  KEY idx_inviter_user (inviter_user_id),
  KEY idx_invite_code (invite_code),
  KEY idx_invitation_status (invitation_status),
  KEY idx_created_at (created_at),
  KEY idx_inviter_status (inviter_user_id, invitation_status),
  CONSTRAINT user_invitations_ibfk_1 FOREIGN KEY (inviter_user_id) REFERENCES users (id) ON DELETE CASCADE,
  CONSTRAINT user_invitations_ibfk_2 FOREIGN KEY (invited_user_id) REFERENCES users (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

UPDATE points_transactions SET source_type = 'invitation' WHERE source_type = 'registration' AND description LIKE '%邀请%';

ALTER TABLE points_transactions MODIFY COLUMN source_type enum('registration','ai_request','export','recharge','subscription','admin_adjust','refund','invitation') NOT NULL;

INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category) VALUES
('invitation_enabled', '1', 'boolean', '邀请功能开关', 'invitation'),
('invitation_points_per_user', '100', 'number', '每邀请一个用户获得的积分', 'invitation'),
('max_invitations_per_user', '10', 'number', '每个用户最多可邀请的用户数量', 'invitation'),
('invitation_code_length', '8', 'number', '邀请码长度', 'invitation'),
('invitation_expire_days', '30', 'number', '邀请码过期天数', 'invitation');
