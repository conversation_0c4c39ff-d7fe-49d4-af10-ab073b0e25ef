// Tailwind CSS 清理工具 - 解决重复导入和样式污染问题
export class TailwindCleaner {

  // 检测Tailwind CDN脚本标签的正则表达式（包括带标记的）
  private static readonly TAILWIND_CDN_REGEX = /<script[^>]*src=["'][^"']*cdn\.tailwindcss\.com[^"']*["'][^>]*>[\s\S]*?<\/script>/gi;

  // 🔧 新增：检测系统插入的Tailwind CDN（带标记）
  private static readonly SYSTEM_TAILWIND_CDN_REGEX = /<script[^>]*src=["'][^"']*cdn\.tailwindcss\.com[^"']*["'][^>]*data-system-inserted=["']tailwind["'][^>]*>[\s\S]*?<\/script>/gi;

  // 🔧 精准检测Tailwind重置样式的特征 - 只匹配真正的Tailwind重置样式
  private static readonly TAILWIND_RESET_SIGNATURES = [
    // 🔧 最强特征：Tailwind的版本标识和许可证信息
    /tailwindcss v\d+\.\d+\.\d+.*MIT License.*https:\/\/tailwindcss\.com/,
    // 🔧 强化：单独的版本标识
    /tailwindcss v\d+\.\d+\.\d+/,
    // 🔧 强化：MIT许可证标识
    /MIT License.*https:\/\/tailwindcss\.com/,
    // Tailwind的全局重置样式特征（通常在样式块开头）
    /\*,::after,::before\s*\{\s*box-sizing:\s*border-box/,
    // 🔧 强化：压缩的Tailwind变量声明（实际出现的模式）
    /\*,\s*::before,\s*::after\{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0/,
    // 🔧 强化：检测大量连续的--tw-变量（Tailwind的核心特征）
    /(--tw-[a-z-]+:\s*[^;]+;\s*){15,}/,
    // 🔧 强化：Tailwind的完整变量声明模式
    /--tw-border-spacing-x:\s*0;--tw-border-spacing-y:\s*0;--tw-translate-x:\s*0;--tw-translate-y:\s*0;--tw-rotate:\s*0;--tw-skew-x:\s*0;--tw-skew-y:\s*0/,
    // Tailwind的完整重置样式块特征（包含大量标签重置）
    /blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre\s*\{\s*margin:\s*0/,
    // Tailwind的表单重置特征
    /button,input,optgroup,select,textarea\s*\{\s*font-family:\s*inherit/,
    // Tailwind的伪元素重置
    /::after,::before\s*\{\s*--tw-content:\s*['"]/,
    // 🔧 新增：检测Tailwind的backdrop变量
    /--tw-backdrop-blur:\s*;--tw-backdrop-brightness:\s*;--tw-backdrop-contrast:\s*;--tw-backdrop-grayscale/,
    // 🔧 新增：检测Tailwind的container类定义
    /\.container\{width:100%\}@media\s*\(min-width:\s*640px\)/
  ];

  // 🔧 检测用户自定义样式的特征
  private static readonly USER_STYLE_SIGNATURES = [
    // 包含具体的CSS类定义（如 .greeting, .container 等）
    /\.[a-zA-Z][a-zA-Z0-9_-]*\s*\{/,
    // 包含具体的HTML标签样式（如 body, h1, div 等）
    /^[a-zA-Z][a-zA-Z0-9]*\s*\{/m,
    // 包含媒体查询
    /@media\s*\(/,
    // 包含具体的颜色值、尺寸等用户定义的样式
    /:\s*(?:rgb|rgba|#[0-9a-fA-F]{3,6}|[0-9]+(?:px|em|rem|vh|vw|%))/
  ];

  // 检测内联Tailwind样式的特征
  private static readonly TAILWIND_INLINE_PATTERNS = [
    /--tw-border-spacing-x:\s*0/,
    /--tw-border-spacing-y:\s*0/,
    /--tw-translate-x:\s*0/,
    /--tw-translate-y:\s*0/,
    /--tw-rotate:\s*0/,
    /--tw-ring-inset/,
    /--tw-ring-offset-width/,
    /tailwindcss v\d+\.\d+\.\d+/
  ];

  /**
   * 🔧 智能分析样式块，区分Tailwind重置样式和用户自定义样式
   * @param styleContent 样式块的内容（不包含<style>标签）
   * @returns 分析结果
   */
  private static analyzeStyleBlock(styleContent: string): {
    isTailwindReset: boolean;
    isUserCustom: boolean;
    confidence: number;
    reasons: string[];
  } {
    const reasons: string[] = [];
    let tailwindScore = 0;
    let userScore = 0;

    // 检测Tailwind重置样式特征
    this.TAILWIND_RESET_SIGNATURES.forEach((pattern, index) => {
      if (pattern.test(styleContent)) {
        tailwindScore += 3; // 高权重
        reasons.push(`检测到Tailwind特征${index + 1}`);
      }
    });

    // 检测用户自定义样式特征
    this.USER_STYLE_SIGNATURES.forEach((pattern, index) => {
      if (pattern.test(styleContent)) {
        userScore += 2; // 中等权重
        reasons.push(`检测到用户样式特征${index + 1}`);
      }
    });

    // 额外的启发式规则
    const lines = styleContent.split('\n').filter(line => line.trim());
    const totalLength = styleContent.length;

    // 🔧 增强的Tailwind检测规则
    // 如果样式块很长且主要是CSS变量，可能是Tailwind
    const cssVarLines = lines.filter(line => line.includes('--tw-'));
    if (cssVarLines.length > 20 && cssVarLines.length / lines.length > 0.7) {
      tailwindScore += 5; // 提高权重
      reasons.push('大量Tailwind CSS变量');
    }

    // 🔧 强化：检测Tailwind的压缩样式特征（一行很长的样式）
    const longLines = lines.filter(line => line.length > 200);
    if (longLines.length > 0 && totalLength > 5000) {
      tailwindScore += 4; // 提高权重
      reasons.push('检测到压缩的Tailwind样式');
    }

    // 🔧 强化：检测单行包含大量--tw-变量（Tailwind压缩样式的强特征）
    const singleLineWithManyVars = styleContent.match(/^[^{]*\{[^}]*--tw-[^}]*--tw-[^}]*--tw-[^}]*--tw-[^}]*--tw-[^}]*\}/m);
    if (singleLineWithManyVars) {
      tailwindScore += 6; // 很高权重
      reasons.push('检测到单行大量Tailwind变量（压缩样式）');
    }

    // 🔧 检测Tailwind的标签重置模式
    const tagResetPattern = /^[a-z1-6,\s]+\{[^}]*margin:\s*0/m;
    if (tagResetPattern.test(styleContent)) {
      tailwindScore += 3;
      reasons.push('检测到Tailwind标签重置模式');
    }

    // 🔧 新增：检测Tailwind的特殊变量组合
    const tailwindVarCombos = [
      /--tw-ring-inset:\s*;--tw-ring-offset-width:\s*0px;--tw-ring-offset-color:\s*#fff/,
      /--tw-gradient-from-position:\s*;--tw-gradient-via-position:\s*;--tw-gradient-to-position/,
      /--tw-backdrop-blur:\s*;--tw-backdrop-brightness:\s*;--tw-backdrop-contrast/
    ];

    tailwindVarCombos.forEach((pattern, index) => {
      if (pattern.test(styleContent)) {
        tailwindScore += 4;
        reasons.push(`检测到Tailwind变量组合${index + 1}`);
      }
    });

    // 🔧 增强的用户样式检测
    // 如果包含具体的类名定义，很可能是用户样式
    const classDefinitions = styleContent.match(/\.[a-zA-Z][a-zA-Z0-9_-]*\s*\{/g);
    if (classDefinitions && classDefinitions.length > 0) {
      userScore += classDefinitions.length * 2; // 提高权重
      reasons.push(`${classDefinitions.length}个CSS类定义`);
    }

    // 如果包含具体的颜色、尺寸等值，很可能是用户样式
    const specificValues = styleContent.match(/:\s*(?:rgb|rgba|#[0-9a-fA-F]{3,6}|[0-9]+(?:px|em|rem|vh|vw|%))/g);
    if (specificValues && specificValues.length > 5) {
      userScore += 3; // 提高权重
      reasons.push(`${specificValues.length}个具体样式值`);
    }

    // 🔧 新增：检测动画和关键帧（用户样式的强特征）
    const animationFeatures = styleContent.match(/@keyframes|animation:|transform:|transition:/g);
    if (animationFeatures && animationFeatures.length > 0) {
      userScore += 4;
      reasons.push(`${animationFeatures.length}个动画特征`);
    }

    // 🔧 新增：检测渐变和复杂背景（用户样式特征）
    const complexStyles = styleContent.match(/linear-gradient|radial-gradient|box-shadow|text-shadow/g);
    if (complexStyles && complexStyles.length > 0) {
      userScore += 3;
      reasons.push(`${complexStyles.length}个复杂样式特征`);
    }

    const totalScore = tailwindScore + userScore;
    const confidence = totalScore > 0 ? Math.max(tailwindScore, userScore) / totalScore : 0;

    return {
      isTailwindReset: tailwindScore > userScore && tailwindScore >= 5, // 提高阈值
      isUserCustom: userScore > tailwindScore && userScore >= 2,
      confidence,
      reasons
    };
  }

  /**
   * 🔧 额外验证：确定是否为Tailwind重置样式
   * @param styleContent 样式内容
   * @returns 是否确定为Tailwind重置样式
   */
  private static isDefinitelyTailwindReset(styleContent: string): boolean {
    // 必须满足以下条件之一才被认为是确定的Tailwind重置样式
    const definiteSignatures = [
      // 1. 🔧 强化：包含Tailwind版本信息和许可证
      /tailwindcss v\d+\.\d+\.\d+.*MIT License/,
      // 2. 🔧 强化：单独的版本信息
      /tailwindcss v\d+\.\d+\.\d+/,
      // 3. 🔧 强化：MIT许可证信息
      /MIT License.*https:\/\/tailwindcss\.com/,
      // 4. 包含大量连续的--tw-变量（超过20个）
      /(--tw-[a-z-]+:\s*[^;]+;\s*){20,}/,
      // 5. 🔧 强化：实际的压缩Tailwind变量模式
      /\*,\s*::before,\s*::after\{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0/,
      // 6. 包含Tailwind的完整变量声明模式
      /--tw-border-spacing-x:\s*0;--tw-border-spacing-y:\s*0;--tw-translate-x:\s*0;--tw-translate-y:\s*0;--tw-rotate:\s*0;--tw-skew-x:\s*0;--tw-skew-y:\s*0;--tw-scale-x:\s*1;--tw-scale-y:\s*1/,
      // 7. 🔧 新增：检测Tailwind的container类定义
      /\.container\{width:100%\}@media\s*\(min-width:\s*640px\)/,
      // 8. 🔧 新增：检测Tailwind的backdrop变量组合
      /--tw-backdrop-blur:\s*;--tw-backdrop-brightness:\s*;--tw-backdrop-contrast:\s*;--tw-backdrop-grayscale:\s*;--tw-backdrop-hue-rotate:\s*;--tw-backdrop-invert:\s*;--tw-backdrop-opacity:\s*;--tw-backdrop-saturate:\s*;--tw-backdrop-sepia/
    ];

    // 检查是否匹配任何确定的特征
    const hasDefiniteSignature = definiteSignatures.some(pattern => pattern.test(styleContent));

    // 🔧 强化：更严格的用户特征检查
    const hasUserFeatures = (
      // 用户定义的CSS类（但排除Tailwind的container类）
      /\.[a-zA-Z][a-zA-Z0-9_-]*\s*\{/.test(styleContent) && !/\.container\{width:100%\}/.test(styleContent) ||
      // 导入语句
      /@import\s+url/.test(styleContent) ||
      // 媒体查询（但排除Tailwind的container媒体查询）
      /@media/.test(styleContent) && !/min-width:\s*\d+px\)\{\.container\{max-width:\d+px\}\}/.test(styleContent) ||
      // 具体颜色值
      /:\s*(?:rgb|rgba|#[0-9a-fA-F]{3,6})/.test(styleContent) ||
      // CSS变量定义（但排除--tw-开头的）
      /--[^t][^w]-/.test(styleContent) ||
      // 复杂的CSS属性
      /(?:background|gradient|transform|transition|animation):\s*[^;]+/.test(styleContent)
    );

    // 🔧 额外检查：如果样式块很长且主要是Tailwind内容
    const isLargeAndMostlyTailwind = (
      styleContent.length > 3000 && // 很长的样式块
      (styleContent.match(/--tw-/g) || []).length > 30 // 包含大量--tw-变量
    );

    // 只有确定是Tailwind且不包含用户特征才返回true，或者是大型Tailwind块
    return (hasDefiniteSignature && !hasUserFeatures) || isLargeAndMostlyTailwind;
  }

  /**
   * 🔧 新增：精准清理系统插入的Tailwind内容（推荐方案）
   * @param html 原始HTML内容
   * @returns 清理后的HTML内容
   */
  static cleanSystemInsertedContent(html: string): string {
    if (!html || !html.trim()) {
      return html;
    }

    console.log('🎯 开始精准清理系统插入的Tailwind内容...');

    let cleanedHtml = html;
    let changeCount = 0;

    // 1. 清理系统插入的Tailwind CDN（保留用户手动添加的）
    const systemCdnMatches = html.match(this.SYSTEM_TAILWIND_CDN_REGEX);
    if (systemCdnMatches && systemCdnMatches.length > 0) {
      console.log(`🗑️ 发现 ${systemCdnMatches.length} 个系统插入的Tailwind CDN，正在清理...`);

      systemCdnMatches.forEach((match, index) => {
        console.log(`📋 系统CDN ${index + 1}: ${match.substring(0, 100)}...`);
        cleanedHtml = cleanedHtml.replace(match, '');
        changeCount++;
      });
    }

    // 2. 清理系统插入的样式标签和动态注入的Tailwind样式
    const parser = new DOMParser();
    const doc = parser.parseFromString(cleanedHtml, 'text/html');

    // 🔧 清理所有系统插入的样式（Tailwind、编辑模式等）
    const systemStyles = doc.querySelectorAll('style[data-system-inserted]');

    if (systemStyles.length > 0) {
      console.log(`🗑️ 发现 ${systemStyles.length} 个系统插入的样式块，正在清理...`);

      systemStyles.forEach((styleElement, index) => {
        const insertType = styleElement.getAttribute('data-system-inserted');
        console.log(`📋 系统样式 ${index + 1} (${insertType}): ${(styleElement.textContent || '').substring(0, 100)}...`);
        styleElement.remove();
        changeCount++;
      });
    }

    // 🔧 强化：清理动态注入的Tailwind重置样式（没有标记的）
    const allStyles = Array.from(doc.querySelectorAll('style'));
    const seenContents = new Set<string>(); // 用于检测重复内容

    allStyles.forEach((styleElement, index) => {
      // 跳过用户自定义样式
      if (styleElement.id === 'loomrun-custom-styles') {
        return;
      }

      const content = styleElement.textContent || '';

      // 🔧 强化：检测重复的样式内容
      if (seenContents.has(content) && content.length > 100) {
        console.log(`🗑️ 移除重复的样式块 ${index + 1}:`, {
          length: content.length,
          preview: content.substring(0, 100) + '...'
        });
        styleElement.remove();
        changeCount++;
        return;
      }

      // 检查是否是动态注入的Tailwind重置样式
      if (this.isDynamicTailwindResetStyle(content)) {
        console.log(`🗑️ 清理动态注入的Tailwind重置样式 ${index + 1}:`, {
          length: content.length,
          preview: content.substring(0, 100) + '...'
        });
        styleElement.remove();
        changeCount++;
      } else if (content.length > 100) {
        // 记录非重复的大型样式块
        seenContents.add(content);
      }
    });

    if (changeCount > 0) {
      // 重新序列化HTML
      cleanedHtml = doc.documentElement.outerHTML;
      // 添加DOCTYPE如果需要
      if (!cleanedHtml.startsWith('<!DOCTYPE')) {
        cleanedHtml = '<!DOCTYPE html>\n' + cleanedHtml;
      }
    }

    if (changeCount > 0) {
      console.log(`✅ 系统内容清理完成，移除了 ${changeCount} 个系统插入的元素`);
    } else {
      console.log('✅ 未发现系统插入的Tailwind内容');
    }

    return cleanedHtml;
  }

  /**
   * 🔧 检测动态注入的Tailwind重置样式（强化版）
   * @param content 样式内容
   * @returns 是否为动态注入的Tailwind重置样式
   */
  private static isDynamicTailwindResetStyle(content: string): boolean {
    // 🔧 强化：检测明确的Tailwind重置样式特征
    const tailwindResetFeatures = [
      // 🔧 最强特征：以Tailwind变量开头的样式块
      /^\*,\s*::before,\s*::after\{--tw-border-spacing-x:0/,
      // Tailwind版本信息
      /tailwindcss v\d+\.\d+\.\d+/,
      // MIT许可证
      /MIT License.*https:\/\/tailwindcss\.com/,
      // 🔧 强化：大量--tw-变量（降低阈值到5个）
      /(--tw-[a-z-]+:\s*[^;]+[;\s]*){5,}/,
      // 🔧 强化：检测Tailwind的基础重置
      /\*,::after,::before\{box-sizing:border-box/,
      // Tailwind的container响应式定义
      /\.container\{width:100%\}@media\s*\(min-width:\s*640px\)/,
      // Tailwind的响应式断点
      /@media\s*\(min-width:\s*(640|768|1024|1280|1536)px\)/
    ];

    // 检查匹配的特征数量
    const matchCount = tailwindResetFeatures.filter(pattern => pattern.test(content)).length;

    // 🔧 强化：检查--tw-变量密度
    const twVariableCount = (content.match(/--tw-/g) || []).length;
    const contentLength = content.length;
    const twDensity = contentLength > 0 ? twVariableCount / contentLength : 0;

    // 🔧 强化：检查是否以Tailwind变量开头（最强特征）
    const startsWithTailwindVars = /^\*,\s*::before,\s*::after\{--tw-/.test(content.trim());

    // 🔧 强化：检查是否包含用户自定义内容
    const hasUserContent = (
      // 用户CSS类（排除Tailwind工具类和container）
      /\.[a-zA-Z][a-zA-Z0-9_-]*\s*\{/.test(content) &&
      !/\.(container|flex|min-h-screen|items-center|justify-center|bg-gray-100|text-center|font-bold|text-gray-800)/.test(content) ||
      // 导入语句
      /@import\s+url\(['"]https:\/\/fonts\.googleapis\.com/.test(content) ||
      // 用户定义的CSS变量（非--tw-开头）
      /--[^t][^w]-/.test(content) ||
      // 具体的颜色值（排除Tailwind默认色）
      /#[0-9a-fA-F]{6}/.test(content) && !/rgb\(59 130 246/.test(content)
    );

    // 🔧 强化判断逻辑：
    // 1. 以Tailwind变量开头（最强特征），或
    // 2. 匹配1个以上Tailwind特征，或
    // 3. --tw-变量密度高（超过0.005），或
    // 4. 内容长度超过500且包含大量--tw-变量
    const isTailwindReset = (
      startsWithTailwindVars ||
      matchCount >= 1 ||
      twDensity > 0.005 ||
      (contentLength > 500 && twVariableCount > 10)
    );

    const result = isTailwindReset && !hasUserContent;

    if (result) {
      console.log('🎯 检测到Tailwind重置样式:', {
        startsWithTailwindVars,
        matchCount,
        twDensity: twDensity.toFixed(4),
        twVariableCount,
        contentLength,
        hasUserContent,
        preview: content.substring(0, 100) + '...'
      });
    }

    return result;
  }

  /**
   * 清理HTML中重复的Tailwind CSS CDN和样式（旧版本，保留兼容性）
   * @param html 原始HTML内容
   * @returns 清理后的HTML内容
   */
  static cleanHTML(html: string): string {
    if (!html || !html.trim()) {
      return html;
    }

    console.log('🧹 开始清理Tailwind CSS重复内容...');
    
    let cleanedHtml = html;
    let changeCount = 0;

    // 1. 清理重复的Tailwind CDN脚本标签
    const cdnMatches = html.match(this.TAILWIND_CDN_REGEX);
    if (cdnMatches && cdnMatches.length > 1) {
      console.log(`🔍 发现 ${cdnMatches.length} 个Tailwind CDN脚本标签，保留第一个`);
      
      // 保留第一个，移除其余的
      let cdnCount = 0;
      cleanedHtml = cleanedHtml.replace(this.TAILWIND_CDN_REGEX, (match) => {
        cdnCount++;
        if (cdnCount === 1) {
          return match; // 保留第一个
        }
        changeCount++;
        return ''; // 移除重复的
      });
    }

    // 2. 🔧 智能检测并移除Tailwind重置样式块（保留用户自定义样式）
    const styleMatches = cleanedHtml.match(/<style[^>]*>([\s\S]*?)<\/style>/gi);
    if (styleMatches && styleMatches.length > 0) {
      console.log(`🔍 发现 ${styleMatches.length} 个样式块，开始智能分析...`);

      styleMatches.forEach((fullMatch, index) => {
        // 提取样式内容（去掉<style>标签）
        const contentMatch = fullMatch.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
        if (contentMatch && contentMatch[1]) {
          const styleContent = contentMatch[1].trim();

          // 智能分析样式块
          const analysis = this.analyzeStyleBlock(styleContent);

          console.log(`📋 样式块 ${index + 1} 分析结果:`, {
            isTailwindReset: analysis.isTailwindReset,
            isUserCustom: analysis.isUserCustom,
            confidence: analysis.confidence.toFixed(2),
            reasons: analysis.reasons,
            preview: styleContent.substring(0, 100) + '...'
          });

          // 🔧 更严格的移除条件：只移除非常确定的Tailwind重置样式
          const shouldRemove = (
            analysis.isTailwindReset &&
            !analysis.isUserCustom &&
            analysis.confidence > 0.7 && // 适中的置信度阈值
            this.isDefinitelyTailwindReset(styleContent) // 额外验证
          );

          if (shouldRemove) {
            console.log(`🗑️ 移除Tailwind重置样式块 ${index + 1}`, {
              confidence: analysis.confidence.toFixed(2),
              reasons: analysis.reasons
            });
            cleanedHtml = cleanedHtml.replace(fullMatch, '');
            changeCount++;
          } else if (analysis.isUserCustom) {
            console.log(`✅ 保留用户自定义样式块 ${index + 1}`);
          } else {
            console.log(`⚠️ 样式块 ${index + 1} 类型不确定，保留以确保安全`, {
              confidence: analysis.confidence.toFixed(2),
              isTailwind: analysis.isTailwindReset,
              isUser: analysis.isUserCustom
            });
          }
        }
      });
    }

    // 3. 清理空的style标签
    cleanedHtml = cleanedHtml.replace(/<style[^>]*>\s*<\/style>/gi, '');

    // 4. 清理多余的空行
    cleanedHtml = cleanedHtml.replace(/\n\s*\n\s*\n/g, '\n\n');

    if (changeCount > 0) {
      console.log(`✅ Tailwind清理完成，移除了 ${changeCount} 个重复/冗余项`);
    } else {
      console.log('✅ 未发现Tailwind重复内容');
    }

    return cleanedHtml;
  }

  /**
   * 🔧 智能检测HTML是否包含重复的Tailwind内容
   * @param html HTML内容
   * @returns 检测结果
   */
  static detectDuplicates(html: string): {
    hasDuplicateCDN: boolean;
    hasDuplicateStyles: boolean;
    cdnCount: number;
    styleCount: number;
    tailwindStyleCount: number;
    userStyleCount: number;
  } {
    const cdnMatches = html.match(this.TAILWIND_CDN_REGEX) || [];
    const allStyleMatches = html.match(/<style[^>]*>([\s\S]*?)<\/style>/gi) || [];

    let tailwindStyleCount = 0;
    let userStyleCount = 0;

    // 智能分析每个样式块
    allStyleMatches.forEach(fullMatch => {
      const contentMatch = fullMatch.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
      if (contentMatch && contentMatch[1]) {
        const styleContent = contentMatch[1].trim();
        const analysis = this.analyzeStyleBlock(styleContent);

        if (analysis.isTailwindReset && !analysis.isUserCustom) {
          tailwindStyleCount++;
        } else if (analysis.isUserCustom) {
          userStyleCount++;
        }
      }
    });

    return {
      hasDuplicateCDN: cdnMatches.length > 1,
      hasDuplicateStyles: tailwindStyleCount > 1, // 只有多个Tailwind样式块才算重复
      cdnCount: cdnMatches.length,
      styleCount: allStyleMatches.length,
      tailwindStyleCount,
      userStyleCount
    };
  }

  /**
   * 验证HTML是否包含Tailwind CSS支持
   * @param html HTML内容
   * @returns 是否包含Tailwind支持
   */
  static hasTailwindSupport(html: string): boolean {
    return this.TAILWIND_CDN_REGEX.test(html);
  }

  /**
   * 确保HTML包含且仅包含一个Tailwind CDN
   * @param html HTML内容
   * @returns 处理后的HTML内容
   */
  static ensureSingleTailwindCDN(html: string): string {
    // 先清理重复的
    let cleanedHtml = this.cleanHTML(html);
    
    // 检查是否还有Tailwind CDN
    if (!this.hasTailwindSupport(cleanedHtml)) {
      console.log('🔧 未发现Tailwind CDN，添加标准CDN链接');
      
      // 🔧 查找head标签并添加带标记的Tailwind CDN
      const headMatch = cleanedHtml.match(/<head[^>]*>/i);
      if (headMatch) {
        const tailwindScript = '\n    <script src="https://cdn.tailwindcss.com" data-system-inserted="tailwind"></script>';
        cleanedHtml = cleanedHtml.replace(
          /<head[^>]*>/i,
          headMatch[0] + tailwindScript
        );
      } else {
        // 如果没有head标签，在html标签后添加
        const htmlMatch = cleanedHtml.match(/<html[^>]*>/i);
        if (htmlMatch) {
          const headWithScript = '\n  <head>\n    <script src="https://cdn.tailwindcss.com" data-system-inserted="tailwind"></script>\n  </head>';
          cleanedHtml = cleanedHtml.replace(
            /<html[^>]*>/i,
            htmlMatch[0] + headWithScript
          );
        }
      }
    }
    
    return cleanedHtml;
  }

  /**
   * 实时清理函数 - 用于样式编辑过程中的实时清理
   * @param html HTML内容
   * @returns 清理后的HTML内容
   */
  static realtimeClean(html: string): string {
    // 实时清理时使用更温和的策略，只移除明显的重复
    const duplicates = this.detectDuplicates(html);
    
    if (duplicates.hasDuplicateCDN || duplicates.hasDuplicateStyles) {
      console.log('🔄 实时清理Tailwind重复内容...', duplicates);
      return this.cleanHTML(html);
    }
    
    return html;
  }

  /**
   * 格式化和优化HTML结构
   * @param html HTML内容
   * @returns 格式化后的HTML内容
   */
  static formatHTML(html: string): string {
    // 基本的HTML格式化和优化
    return html
      .replace(/>\s+</g, '><') // 移除标签间的多余空白
      .replace(/\n\s*\n\s*\n/g, '\n\n') // 清理多余空行
      .replace(/\s+$/gm, '') // 移除行尾空白
      .trim();
  }
} 