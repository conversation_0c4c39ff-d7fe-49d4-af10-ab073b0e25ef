"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Coins, TrendingUp, TrendingDown } from "lucide-react";

export default function TestComponentsPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">组件测试页面</h1>
      
      {/* 测试 Card 组件 */}
      <Card>
        <CardHeader>
          <CardTitle>Card 组件测试</CardTitle>
          <CardDescription>测试 Card、CardHeader、CardTitle、CardDescription、CardContent 组件</CardDescription>
        </CardHeader>
        <CardContent>
          <p>这是 CardContent 的内容。如果你能看到这个内容，说明 Card 组件工作正常。</p>
        </CardContent>
      </Card>

      {/* 测试 Badge 组件 */}
      <Card>
        <CardHeader>
          <CardTitle>Badge 组件测试</CardTitle>
          <CardDescription>测试不同变体的 Badge 组件</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            <Badge>Default Badge</Badge>
            <Badge variant="secondary">Secondary Badge</Badge>
            <Badge variant="destructive">Destructive Badge</Badge>
            <Badge variant="outline">Outline Badge</Badge>
            <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
              Custom Badge
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* 测试 Tabs 组件 */}
      <Card>
        <CardHeader>
          <CardTitle>Tabs 组件测试</CardTitle>
          <CardDescription>测试 Tabs、TabsList、TabsTrigger、TabsContent 组件</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tab1">
            <TabsList>
              <TabsTrigger value="tab1">标签页 1</TabsTrigger>
              <TabsTrigger value="tab2">标签页 2</TabsTrigger>
              <TabsTrigger value="tab3">标签页 3</TabsTrigger>
            </TabsList>
            <TabsContent value="tab1">
              <p>这是标签页 1 的内容。</p>
            </TabsContent>
            <TabsContent value="tab2">
              <p>这是标签页 2 的内容。</p>
            </TabsContent>
            <TabsContent value="tab3">
              <p>这是标签页 3 的内容。</p>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 测试积分卡片样式 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 border-blue-200 dark:border-blue-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">当前积分</p>
                <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">1,234</p>
              </div>
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                <Coins className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50 border-green-200 dark:border-green-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">累计获得</p>
                <p className="text-3xl font-bold text-green-700 dark:text-green-300">5,678</p>
              </div>
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/50 dark:to-red-900/50 border-red-200 dark:border-red-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600 dark:text-red-400">累计消费</p>
                <p className="text-3xl font-bold text-red-700 dark:text-red-300">4,444</p>
              </div>
              <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                <TrendingDown className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 测试按钮 */}
      <Card>
        <CardHeader>
          <CardTitle>Button 组件测试</CardTitle>
          <CardDescription>测试不同变体的 Button 组件</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            <Button>Default Button</Button>
            <Button variant="secondary">Secondary Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="ghost">Ghost Button</Button>
            <Button variant="destructive">Destructive Button</Button>
            <Button size="sm">Small Button</Button>
            <Button size="lg">Large Button</Button>
          </div>
        </CardContent>
      </Card>

      {/* 状态指示 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-green-600">✅ 组件测试结果</CardTitle>
          <CardDescription>如果你能看到上面所有的组件内容，说明所有组件都工作正常</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">✅ Card</Badge>
              <span className="text-sm">Card、CardHeader、CardTitle、CardDescription、CardContent</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">✅ Badge</Badge>
              <span className="text-sm">Badge 组件及其变体</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">✅ Tabs</Badge>
              <span className="text-sm">Tabs、TabsList、TabsTrigger、TabsContent</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">✅ Button</Badge>
              <span className="text-sm">Button 组件及其变体</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">✅ Icons</Badge>
              <span className="text-sm">Lucide React 图标</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 导航按钮 */}
      <div className="flex gap-4">
        <Button onClick={() => window.location.href = '/points'}>
          前往积分页面
        </Button>
        <Button variant="outline" onClick={() => window.location.href = '/points-simple'}>
          前往简化积分页面
        </Button>
        <Button variant="ghost" onClick={() => window.location.href = '/'}>
          返回首页
        </Button>
      </div>
    </div>
  );
}
